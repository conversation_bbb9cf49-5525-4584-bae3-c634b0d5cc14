const { expect } = require('chai');
const { assert } = require('chai');
const request = require('supertest');
const moment = require('moment');
const { DateTime } = require('luxon');
const sinon = require('sinon');
const ct = require('countries-and-timezones');
const {
  app, mongoose, validImagePath, validVideoPath, validAudioPath, waitMs, initSocket, getSocketPromise, destroySocket,
} = require('./common');
const LocationStat = require('../models/location-stat');
const cityLib = require('../lib/city');
const Metric = require('../models/metric');
const ATT = require('../models/att');
const {
  notifs, reset, waitFor, fakeTwilioClient, fakeSES,
} = require('./stub');
const User = require('../models/user');
const Message = require('../models/message');
const PurchaseReceipt = require('../models/purchase-receipt');
const NeureonPurchaseReceipt = require('../models/neuron-purchase-receipt');
const CoinPurchaseReceipt = require('../models/coin-purchase-receipt');
const SuperLikePurchaseReceipt = require('../models/super-like-purchase-receipt');
const Interest = require('../models/interest');
const AiNotification = require('../models/ai-notifications');
const mailchimp = require('../lib/mailchimp-transactional');
const ses = require('../lib/ses');
const basic = require('../lib/basic');
const socialLib = require('../lib/social');
const promptsLib = require('../lib/prompts');

const msPerHour = 60 * 60 * 1000;
const { smsNotifications } = require('../worker/lib/push');
const { twilioNumber } = require('../lib/twilio-config');
const { fakeAdminMessaging, getDeletedUids } = require('./stub');
const {
  initApp, setNotificationSettings, recordCityMetrics, setLocation, setFcmToken, cityNewUserPush, setFirstName,
} = require('./helper/api');
const { generateRandomLocation, mockLocs } = require('./helper/location');
const constants = require('../lib/constants');
const metricsLib = require('../lib/metrics');
const kochavaLib = require('../lib/kochava');
const { DAILY_PROFILE_LIMIT } = require('../lib/constants');
const Chat = require('../models/chat');
const { validGoogleReceipt, validAppleReceipt } = require('./iap');
const { getCommonTranslatedTags, translateEmail, getUnsubLink } = require('../lib/email');
const { BOO_BOT_ID } = require('../lib/chat');
const { parse } = require('flatted');
const cfsign = require('aws-cloudfront-sign');
const momentTZ = require('moment-timezone');
const { getTodaysTheme, getAnalyticsLabel } = require('../worker/lib/notifications/helper');
const BannedUser = require('../models/banned-user');
const BannedFile = require('../models/banned-file');
const bunny = require('../lib/bunny');

async function createBotUser() {
  const botID = BOO_BOT_ID;
  const res = await request(app)
    .get('/v1/user')
    .set('authorization', botID);
  expect(res.status).to.equal(200);
}

it('should clean up deleted users and their data after 3 years', async () => {
  const payload = {
    img: 'base-64-image-data',
    secure: {
      version: "2.7.0",
      token: "token-data",
      verification: "verification-data",
      signature: "signature-data",
    },
  };

  // --- Step 1: Create users and upload pictures ---
  for (let uid = 0; uid < 10; uid++) {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.13.65' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', uid)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    if (uid % 2 === 0) {
      res = await request(app)
        .post('/v1/user/profileVerificationPicture/liveness')
        .set('authorization', uid)
        .send(payload);
      expect(res.status).to.equal(200);
      expect(res.body.verificationStatus).to.equal('verified');
    }
  }

  // --- Step 2: Stub dependencies ---
  fakeS3.getObject = () => ({
    promise: () => Promise.resolve({
      Body: Buffer.from(`mock file content`),
      Metadata: { 'mock-metadata': 'value' },
    }),
  });

  setMockPromptResponse('{"ban": true, "reason": "scam"}');

  fakeRekognition.indexFaces = ({ Image }) => ({
    promise: () => Promise.resolve({
      FaceRecords: [{ Face: { FaceId: Image.S3Object.Name } }],
    }),
  });

  // --- Step 3: Report and ban users ---
  for (let uid = 1; uid < 10; uid++) {
    const res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: uid.toString(),
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    const user = await User.findById(uid.toString());
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedBy).to.equal('openai');
    expect(user.bannedReason).to.equal('Scamming');
  }

  // --- Step 4: Mark deletable banned users ---
  const deletableUserIds = [];
  const allBannedUsers = await BannedUser.find({});
  expect(allBannedUsers.length).to.equal(9);

  for (const user of allBannedUsers) {
    const yearsAgo = user.userData.verification.status === 'verified' ? 3 : 1;
    user.deletedAt = moment().subtract(yearsAgo, 'years').toDate();
    await user.save();
    if (yearsAgo === 3) deletableUserIds.push(user.user);
  }

  const allBannedFiles = await BannedFile.find({});
  expect(allBannedFiles.length).to.equal(9);

  // --- Step 5: Extract users eligible for deletion ---
  const cutoffDate = moment().subtract(3, 'years').toDate();
  const deletableUsers = await BannedUser.find({ deletedAt: { $lte: cutoffDate } });
  expect(deletableUsers.length).to.equal(4);

  const expectedPictures = deletableUsers.flatMap(u => u.userData.pictures);
  const expectedFaceIds = new Set(deletableUsers.flatMap(u => u.faceIds));

  // --- Step 6: Stub Rekognition and S3 deletion ---
  const rekognitionDeleted = new Set();

  fakeRekognition.deleteFaces = ({ FaceIds }) => {
    FaceIds.forEach(id => rekognitionDeleted.add(id));
    return {
      promise: () => Promise.resolve({ DeletedFaces: FaceIds }),
    };
  };

  // --- Step 7: Trigger cleanup ---
  const res = await request(app)
    .post('/v1/worker/cleanupDeletedUsers')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  // --- Step 8: Verify deletions ---
  expect(deletedKeys.sort()).to.eql(expectedPictures.sort());
  expect(Array.from(rekognitionDeleted).sort()).to.eql(Array.from(expectedFaceIds).sort());

  // --- Step 9: Confirm records are removed ---
  const remainingUsers = await BannedUser.find({});
  expect(remainingUsers.length).to.equal(5);
  remainingUsers.forEach(user => {
    expect(deletableUserIds).to.not.include(user.user);
  });

  const remainingFiles = await BannedFile.find({});
  expect(remainingFiles.length).to.equal(5);
  remainingFiles.forEach(file => {
    expect(deletableUserIds).to.not.include(file.user);
  });
});

it('delete chats and chat files after 30 days', async () => {
  // --- Create users ---
  for (let uid = 0; uid < 2; uid++) {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.13.65' });
    expect(res.status).to.equal(200);

    user = await User.findById(uid);
    user.verification.status = 'verified';
    user.premiumExpiration = Date.now() + 86400000;
    await user.save();
  }

  // --- Stub dependencies ---
  sinon.stub(constants, 'getS3BucketResized').returns('MOCK_S3_BUCKET_RESIZED');

  let s3DeletedKeys = [];
  fakeS3.deleteObjects = (params) => {
    console.log(`fakeS3.deleteObjects: ${JSON.stringify(params)}`);
    s3DeletedKeys = s3DeletedKeys.concat(params.Delete.Objects.map(o => `${params.Bucket}/${o.Key}`));
    return {
      promise: () => Promise.resolve({}),
    };
  };

  let imageKeys = [];
  fakeS3.listObjectsV2 = function(params) {
    console.log(`fakeS3.listObjectsV2: ${JSON.stringify(params)}`);
    const impl = function (resolve, reject) {
      resolve({
        Contents: imageKeys.filter(x => x.includes(params.Prefix)).map(key => ({ Key: key })),
      });
    };
    return {
      promise: () => new Promise(impl),
    };
  };

  // --- Set up deleted chat with files ---
  res = await request(app)
    .patch('/v1/user/sendDirectMessage')
    .set('authorization', 0)
    .send({
      user: '1',
      message: 'Hi',
      price: 0,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/approve')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/message/image')
    .set('authorization', 0)
    .query({ recipient: '1' })
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/message/audio')
    .set('authorization', 1)
    .query({ recipient: '0' })
    .attach('audio', validAudioPath);
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/unmatch')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  chat = await Chat.findOne().sort('-_id');
  chat.createdAt = new Date('2025-09-05');
  await chat.save();

  // --- Set up deleted chat without files ---
  res = await request(app)
    .patch('/v1/user/sendDirectMessage')
    .set('authorization', 0)
    .send({
      user: '1',
      message: 'Hi',
      price: 0,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/approve')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/unmatch')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  chat = await Chat.findOne().sort('-_id');
  chat.createdAt = new Date('2025-09-05');
  await chat.save();

  // --- Set up deleted chat with files without hasMediaFiles (backwards compatibility test) ---
  res = await request(app)
    .patch('/v1/user/sendDirectMessage')
    .set('authorization', 0)
    .send({
      user: '1',
      message: 'Hi',
      price: 0,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/approve')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/message/video')
    .set('authorization', 0)
    .query({
      recipient: '1',
    })
    .attach('video', validVideoPath);
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/unmatch')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  chat = await Chat.findOne().sort('-_id');
  chat.createdAt = new Date('2025-09-01');
  chat.hasMediaFiles = undefined;
  await chat.save();

  // --- Check data ---
  chats = await Chat.find();
  expect(chats.length).to.equal(3);

  messages = await Message.find();
  expect(messages.length).to.equal(6);

  imageKeys = messages.map(x => x.image || x.video || x.audio).filter(Boolean);
  console.log(imageKeys);
  expect(imageKeys.length).to.equal(3);

  expect(s3DeletedKeys.length).to.equal(0);

  // --- Trigger delete: nothing deleted yet ---
  res = await request(app)
    .post('/v1/worker/deleteChats')
  expect(res.status).to.equal(200);

  chats = await Chat.find();
  expect(chats.length).to.equal(3);

  messages = await Message.find();
  expect(messages.length).to.equal(6);

  expect(s3DeletedKeys.length).to.equal(0);

  // --- Fast forward 31 days, then trigger delete ---
  // add toFake to avoid the test getting stuck due to the use of 'bottleneck' library in deleteChats
  clock = sinon.useFakeTimers({
    now: Date.now(),
    toFake: ['Date'],
  });
  clock.tick(31 * 24 * msPerHour);

  res = await request(app)
    .post('/v1/worker/deleteChats')
  expect(res.status).to.equal(200);

  chats = await Chat.find();
  expect(chats.length).to.equal(0);

  messages = await Message.find();
  expect(messages.length).to.equal(0);

  console.log(s3DeletedKeys);
  expect(s3DeletedKeys.sort()).to.eql([
    `MOCK_S3_BUCKET/${imageKeys[0]}`,
    `MOCK_S3_BUCKET/${imageKeys[1]}`,
    `MOCK_S3_BUCKET/${imageKeys[2]}`,
    `MOCK_S3_BUCKET_RESIZED/${imageKeys[0]}`,
    `MOCK_S3_BUCKET_RESIZED/${imageKeys[1]}`,
    `MOCK_S3_BUCKET_RESIZED/${imageKeys[2]}`,
  ].sort());
});

describe('daily push', async () => {
  beforeEach(async () => {
    const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 12);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        timezone,
        locale: 'en',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({
        fcmToken: '0',
      });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: '0' });
    user.metrics.lastSeen = new Date(2000, 1, 1);
    await user.save();

    reset();
  });

  it('looking for dating', async () => {
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['female'],
      });
    expect(res.status).to.equal(200);

    const numNotifs = 1;
    clock = sinon.useFakeTimers(new Date());
    for (let i = 0; i < numNotifs; i++) {
      clock.tick(24 * msPerHour);
      res = await request(app)
        .post('/v1/worker/dailyPush')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
    }
    clock.restore();

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == numNotifs || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(numNotifs);
  });

  it('looking for friends', async () => {
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        friends: ['female'],
      });
    expect(res.status).to.equal(200);

    const numNotifs = 1;
    clock = sinon.useFakeTimers(new Date());
    for (let i = 0; i < numNotifs; i++) {
      clock.tick(24 * msPerHour);
      res = await request(app)
        .post('/v1/worker/dailyPush')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
    }
    clock.restore();

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == numNotifs || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(numNotifs);
  });

  it('non-English locale', async () => {
    user = await User.findOne({ _id: '0' });
    user.locale = 'de';
    await user.save();

    res = await request(app)
      .post('/v1/worker/dailyPush')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
  });

  it('active', async () => {
    user = await User.findOne({ _id: '0' });
    user.metrics.lastSeen = Date.now();
    await user.save();

    res = await request(app)
      .post('/v1/worker/dailyPush')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);
  });

  it('local time not noon', async () => {
    const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 1);
    user = await User.findOne({ _id: '0' });
    user.timezone = timezone;
    await user.save();

    res = await request(app)
      .post('/v1/worker/dailyPush')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);
  });

  it('daily push disabled', async () => {
    const settings = {};
    settings.dailyFacts = false;
    res = await request(app)
      .put('/v1/user/notificationSettings')
      .set('authorization', 0)
      .send({
        pushNotificationSettings: settings,
      });

    expect(res.status).to.equal(200);
    res = await request(app)
      .post('/v1/worker/dailyPush')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);
  });

  it('daily push analytics label', async () => {

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['female'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/worker/dailyPush')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.fcmOptions.analyticsLabel).to.equal('daily-push-dating');
    reset();

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: [],
        friends: ['female'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/worker/dailyPush')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.fcmOptions.analyticsLabel).to.equal('daily-push-friends');
    reset();

  });
});

describe('daily push, new notification setting', async () => {
  beforeEach(async () => {
    const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 12);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        timezone,
        locale: 'en',
      });
    expect(res.status).to.equal(200);

    user = await User.findOne({_id: 0})
    user.pushNotificationSettings.dailyPush = false
    user.appVersion = '1.13.71'
    await user.save()
    console.log('user appVersion :', user.appVersion)
    console.log('user pushNotificationSettings :', user.pushNotificationSettings)

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({
        fcmToken: '0',
      });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: '0' });
    user.metrics.lastSeen = new Date(2000, 1, 1);
    await user.save();

    reset();
  });

  it('looking for dating', async () => {
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['female'],
      });
    expect(res.status).to.equal(200);

    const numNotifs = 1;
    clock = sinon.useFakeTimers(new Date());
    for (let i = 0; i < numNotifs; i++) {
      clock.tick(24 * msPerHour);
      res = await request(app)
        .post('/v1/worker/dailyPush')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
    }
    clock.restore();

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == numNotifs || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(numNotifs);
  });

  it('looking for friends', async () => {
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        friends: ['female'],
      });
    expect(res.status).to.equal(200);

    const numNotifs = 1;
    clock = sinon.useFakeTimers(new Date());
    for (let i = 0; i < numNotifs; i++) {
      clock.tick(24 * msPerHour);
      res = await request(app)
        .post('/v1/worker/dailyPush')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
    }
    clock.restore();

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == numNotifs || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(numNotifs);
  });

  it('non-English locale', async () => {
    user = await User.findOne({ _id: '0' });
    user.locale = 'de';
    await user.save();

    res = await request(app)
      .post('/v1/worker/dailyPush')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
  });

  it('active', async () => {
    user = await User.findOne({ _id: '0' });
    user.metrics.lastSeen = Date.now();
    await user.save();

    res = await request(app)
      .post('/v1/worker/dailyPush')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);
  });

  it('local time not noon', async () => {
    const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 1);
    user = await User.findOne({ _id: '0' });
    user.timezone = timezone;
    await user.save();

    res = await request(app)
      .post('/v1/worker/dailyPush')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);
  });

  it('daily facts disabled', async () => {
    const settings = {};
    settings.dailyFacts = false;
    res = await request(app)
      .put('/v1/user/notificationSettings')
      .set('authorization', 0)
      .send({
        pushNotificationSettings: settings,
      });

    expect(res.status).to.equal(200);
    res = await request(app)
      .post('/v1/worker/dailyPush')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);
  });

  it('daily push analytics label', async () => {
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['female'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/worker/dailyPush')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.fcmOptions.analyticsLabel).to.equal('daily-push-dating');
    reset();

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: [],
        friends: ['female'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/worker/dailyPush')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.fcmOptions.analyticsLabel).to.equal('daily-push-friends');
    reset();
  });
});

/*
it('day 1 universes push', async () => {
  try {
    // January 7, 2021 21:57:00 UTC
    initDateMs = 1610056620001;
    clock = sinon.useFakeTimers({
      now: initDateMs,
    });

    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 15);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.27',
        locale: 'en',
        timezone,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({
        fcmToken: 'token0',
      });
    expect(res.status).to.equal(200);

    // no notification
    res = await request(app)
      .post('/v1/worker/day1UniversesPush')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);
    reset();

    // 1 day
    clock.tick(1 * 24 * msPerHour);

    // notification
    res = await request(app)
      .post('/v1/worker/day1UniversesPush')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.notification.title).to.equal('Post in the Universes');
    expect(notifs.recent.notification.body).to.equal("Souls who post and comment make a lot more connections. Plus it's fun, try it out!");
    expect(notifs.recent.data).to.eql({openPage:'universes'});
    expect(notifs.recent.token).to.eql('token0');
    reset();

    user = await User.findById('0');
    expect(user.events.day_1_universes_push).to.equal(1);
    user.config.day_1_universes_push = false;
    await user.save();

    res = await request(app)
      .post('/v1/worker/day1UniversesPush')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);

  } finally {
    clock.restore();
  }
});
*/

it('day 1 spirit realm push', async () => {
  try {
    // January 7, 2021 21:57:00 UTC
    initDateMs = 1610056620001;
    clock = sinon.useFakeTimers({
      now: initDateMs,
    });

    const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 18);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        locale: 'en',
        timezone,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({
        fcmToken: 'token0',
      });
    expect(res.status).to.equal(200);

    // no notification
    res = await request(app)
      .post('/v1/worker/day1SpiritRealmPush')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);
    reset();

    // 1 day
    clock.tick(1 * 24 * msPerHour);

    // no notification
    res = await request(app)
      .post('/v1/worker/day1SpiritRealmPush')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);
    reset();

    // hide city
    res = await request(app)
      .put('/v1/user/hideCity')
      .set('authorization', 0)
      .send({
        hideCity: true,
      });
    expect(res.status).to.equal(200);

    // notification
    res = await request(app)
      .post('/v1/worker/day1SpiritRealmPush')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.notification.title).to.equal('Try the Spirit Realm');
    expect(notifs.recent.notification.body).to.equal("Activate the ultimate invisibility cloak. See other people, but other people can't see you. 👀");
    expect(notifs.recent.token).to.eql('token0');
    reset();

    // not translated yet
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        timezone,
        locale: 'haw',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/worker/day1SpiritRealmPush')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);

  } finally {
    clock.restore();
  }
});

it('day 1 unlimited loves push', async () => {
  try {
    // ensure we begin with a clean slate
    await Metric.deleteMany();
    await metricsLib.loadMostRecentMetrics();

    // January 7, 2021 21:57:00 UTC
    initDateMs = 1610056620001;
    clock = sinon.useFakeTimers({
      now: initDateMs,
    });

    const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 21);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        locale: 'en',
        timezone,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({
        fcmToken: 'token0',
      });
    expect(res.status).to.equal(200);

    // no notification
    res = await request(app)
      .post('/v1/worker/day1UnlimitedLovesPush')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);
    reset();

    // 1 day
    clock.tick(1 * 24 * msPerHour);

    // notification
    res = await request(app)
      .post('/v1/worker/day1UnlimitedLovesPush')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.notification.title).to.equal("Don't wait until tomorrow");
    expect(notifs.recent.notification.body).to.equal("Join 0 souls who have unlocked Boo Infinity and unlimited loves.");
    expect(notifs.recent.token).to.eql('token0');
    reset();

    // mock metric
    await Metric.create({
      numUniqueUsersWhoPurchasedInfinity: 1000000,
    });
    await metricsLib.loadMostRecentMetrics();

    // test formatting
    res = await request(app)
      .post('/v1/worker/day1UnlimitedLovesPush')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.notification.title).to.equal("Don't wait until tomorrow");
    expect(notifs.recent.notification.body).to.equal("Join 1,000,000 souls who have unlocked Boo Infinity and unlimited loves.");
    expect(notifs.recent.token).to.eql('token0');
    reset();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        timezone,
        countryLocale: 'en_IN',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/worker/day1UnlimitedLovesPush')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.notification.title).to.equal("Don't wait until tomorrow");
    expect(notifs.recent.notification.body).to.equal("Join 10,00,000 souls who have unlocked Boo Infinity and unlimited loves.");
    expect(notifs.recent.token).to.eql('token0');
    reset();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        timezone,
        countryLocale: 'en_DE',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/worker/day1UnlimitedLovesPush')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.notification.title).to.equal("Don't wait until tomorrow");
    expect(notifs.recent.notification.body).to.equal("Join 1.000.000 souls who have unlocked Boo Infinity and unlimited loves.");
    expect(notifs.recent.token).to.eql('token0');
    reset();

    // not translated yet
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        timezone,
        locale: 'haw',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/worker/day1UnlimitedLovesPush')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);

  } finally {
    clock.restore();
  }
});

it('day 2 push', async () => {
  try {
    // January 7, 2021 21:57:00 UTC
    initDateMs = 1610056620001;
    clock = sinon.useFakeTimers({
      now: initDateMs,
    });

    const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 18);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        locale: 'en',
        timezone,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({
        fcmToken: 'token0',
      });
    expect(res.status).to.equal(200);

    // no notification
    res = await request(app)
      .post('/v1/worker/day2Push')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);
    reset();

    // 2 days
    clock.tick(2 * 24 * msPerHour);

    // notification
    res = await request(app)
      .post('/v1/worker/day2Push')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.notification.title).to.equal('Patience is key');
    expect(notifs.recent.notification.body).to.equal('But Boo Infinity members receive up to 3X more matches and find their best match faster.');
    expect(notifs.recent.token).to.eql('token0');
    reset();

    // not translated yet
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        timezone,
        locale: 'haw',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/worker/day2Push')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);

  } finally {
    clock.restore();
  }
});

it('day 3 push', async () => {
  try {
    // January 7, 2021 21:57:00 UTC
    initDateMs = 1610056620001;
    clock = sinon.useFakeTimers({
      now: initDateMs,
    });

    const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 21);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        locale: 'en',
        timezone,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({
        fcmToken: 'token0',
      });
    expect(res.status).to.equal(200);

    // no notification
    res = await request(app)
      .post('/v1/worker/day3Push')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);
    reset();

    // 3 day
    clock.tick(3 * 24 * msPerHour);

    // notification
    res = await request(app)
      .post('/v1/worker/day3Push')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.notification.title).to.equal("Pro Tip: Send a DM");
    expect(notifs.recent.notification.body).to.equal("Stand out with personalized greetings.");
    expect(notifs.recent.token).to.eql('token0');
    reset();

    // not translated yet
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        timezone,
        locale: 'haw',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/worker/day3Push')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);

  } finally {
    clock.restore();
  }
});

it('day 4 push', async () => {
  try {
    // January 7, 2021 21:57:00 UTC
    initDateMs = 1610056620001;
    clock = sinon.useFakeTimers({
      now: initDateMs,
    });

    const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 21);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        locale: 'en',
        timezone,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({
        fcmToken: 'token0',
      });
    expect(res.status).to.equal(200);

    // no notification
    res = await request(app)
      .post('/v1/worker/day4Push')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);
    reset();

    // 4 day
    clock.tick(4 * 24 * msPerHour);

    // notification
    res = await request(app)
      .post('/v1/worker/day4Push')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.notification.title).to.equal("Take the chance on someone");
    expect(notifs.recent.notification.body).to.equal("Don't be afraid to send that super love.");
    expect(notifs.recent.token).to.eql('token0');
    reset();

    // not translated yet
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        timezone,
        locale: 'haw',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/worker/day4Push')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);

  } finally {
    clock.restore();
  }
});

it('day 5 push', async () => {
  try {
    // January 7, 2021 21:57:00 UTC
    initDateMs = 1610056620001;
    clock = sinon.useFakeTimers({
      now: initDateMs,
    });

    const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 21);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        locale: 'en',
        timezone,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({
        fcmToken: 'token0',
      });
    expect(res.status).to.equal(200);

    // no notification
    res = await request(app)
      .post('/v1/worker/day5Push')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);
    reset();

    // 5 day
    clock.tick(5 * 24 * msPerHour);

    // notification
    res = await request(app)
      .post('/v1/worker/day5Push')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.notification.title).to.equal("Meet souls all around the world");
    expect(notifs.recent.notification.body).to.equal("Teleport using Boo Infinity, or set your recommendations to global mode and explore the alternate dimensions!");
    expect(notifs.recent.token).to.eql('token0');
    reset();

    // not translated yet
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        timezone,
        locale: 'haw',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/worker/day5Push')
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);

  } finally {
    clock.restore();
  }
});

it('day 7 second flash sale', async () => {
  try {
    const stub = sinon.stub(ses, 'sendTemplatedEmail')
      .callsFake((params) => {
        console.log('Fake sendTemplatedEmail', JSON.stringify(params));
        const impl = function (resolve, reject) {
          resolve({});
        };
        return new Promise(impl);
        return {
          promise: () => new Promise(impl),
        };
      });

    // January 7, 2021 21:57:00 UTC
    initDateMs = 1610056620001;
    clock = sinon.useFakeTimers({
      now: initDateMs,
    });

    // day 0 first flash sale
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.8.8',
        locale: 'en',
      });
    expect(res.status).to.equal(200);
    expect(res.body.user.premiumFlashSale.discount).to.equal(50);
    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({
        fcmToken: '0',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 0)
      .send({ firstName: 'name' });
    expect(res.status).to.equal(200);

    stub.resetHistory();

    // no notification
    res = await request(app)
      .post('/v1/worker/secondSale')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);
    expect(stub.callCount).to.equal(0);

    // flash sale should end
    clock.tick(4 * 24 * msPerHour);

    // no notification
    res = await request(app)
      .post('/v1/worker/secondSale')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);
    expect(stub.callCount).to.equal(0);

    // second flash sale
    clock.tick(3 * 24 * msPerHour);

    // notification
    res = await request(app)
      .post('/v1/worker/secondSale')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(1);
    expect(stub.callCount).to.equal(1);
    expect(stub.getCall(0).args[0].Template).to.equal('flash-sale-v6');
    reset();
    stub.resetHistory();

    // wait one day
    clock.tick(4 * 24 * msPerHour);

    // no notification
    res = await request(app)
      .post('/v1/worker/secondSale')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);
    expect(stub.callCount).to.equal(0);

    // get flash sale
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.premiumFlashSale.discount).to.equal(50);

    // flash sale should end
    clock.tick(4 * 24 * msPerHour);

    // no notification
    res = await request(app)
      .post('/v1/worker/secondSale')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);
    expect(stub.callCount).to.equal(0);

    // 4 more days - no flash sale
    clock.tick(4 * 24 * msPerHour);

    // no notification
    res = await request(app)
      .post('/v1/worker/')
      .set('authorization', 0)
      .send({ route: 'secondSale' });
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);
    expect(stub.callCount).to.equal(0);
  } finally {
    clock.restore();
  }
});

it('day 7 second flash sale, on users with same deviceId APP-488', async () => {
  try {
    const stub = sinon.stub(ses, 'sendTemplatedEmail')
      .callsFake((params) => {
        console.log('Fake sendTemplatedEmail', JSON.stringify(params));
        const impl = function (resolve, reject) {
          resolve({});
        };
        return new Promise(impl);
        return {
          promise: () => new Promise(impl),
        };
      });

    // January 7, 2021 21:57:00 UTC
    initDateMs = 1610056620001;
    clock = sinon.useFakeTimers({
      now: initDateMs,
    });

    for (let i = 0; i < 4; i++) {
      // day 0 first flash sale
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({
          appVersion: '1.13.12',os: 'ios',locale: 'en',deviceId: 'same-device-id'
        });
      expect(res.status).to.equal(200);
      if(i === 0){
        expect(res.body.user.premiumFlashSale.discount).to.equal(50);
      }else{
        expect(res.body.user.premiumFlashSale).to.be.undefined;
      }

      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', i)
        .send({
          fcmToken: `${i}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', 0)
        .send({ firstName: 'name' });
      expect(res.status).to.equal(200);

      stub.resetHistory();

      // no notification
      res = await request(app)
        .post('/v1/worker/secondSale')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      expect(notifs.numSent).to.equal(0);
      expect(stub.callCount).to.equal(0);

      // flash sale should end
      clock.tick(4 * 24 * msPerHour);

      // no notification
      res = await request(app)
        .post('/v1/worker/secondSale')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      expect(notifs.numSent).to.equal(0);
      expect(stub.callCount).to.equal(0);

      // second flash sale
      clock.tick(3 * 24 * msPerHour);

      // notification
      res = await request(app)
        .post('/v1/worker/secondSale')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      if(i === 0){
        expect(notifs.numSent).to.equal(1);
        expect(stub.callCount).to.equal(1);
        expect(stub.getCall(0).args[0].Template).to.equal('flash-sale-v6');
      }else{
        expect(notifs.numSent).to.equal(0);
        expect(stub.callCount).to.equal(0);
      }

      reset();
      stub.resetHistory();

      // wait one day
      clock.tick(4 * 24 * msPerHour);

      // no notification
      res = await request(app)
        .post('/v1/worker/secondSale')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      expect(notifs.numSent).to.equal(0);
      expect(stub.callCount).to.equal(0);

      // get flash sale
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      if(i === 0){
        expect(res.body.user.premiumFlashSale.discount).to.equal(50);
      }else{
        expect(res.body.user.premiumFlashSale).to.be.undefined;
      }

      // flash sale should end
      clock.tick(4 * 24 * msPerHour);

      // no notification
      res = await request(app)
        .post('/v1/worker/secondSale')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      expect(notifs.numSent).to.equal(0);
      expect(stub.callCount).to.equal(0);

      // 4 more days - no flash sale
      clock.tick(4 * 24 * msPerHour);

      // no notification
      res = await request(app)
        .post('/v1/worker/')
        .set('authorization', i)
        .send({ route: 'secondSale' });
      expect(res.status).to.equal(200);
      expect(notifs.numSent).to.equal(0);
      expect(stub.callCount).to.equal(0);

      if(i === 0){
        // user 0 made purchase
        res = await request(app)
          .put('/v1/user/purchasePremium')
          .set('authorization', i)
          .send({
            receipt: validAppleReceipt,
          });
        expect(res.status).to.equal(200);
      }
    }
  } finally {
    clock.restore();
  }
});

it('monthly recurring flash sale', async () => {
  try {
    const stub = sinon.stub(ses, 'sendTemplatedEmail')
      .callsFake((params) => {
        console.log('Fake sendTemplatedEmail', JSON.stringify(params));
        const impl = function (resolve, reject) {
          resolve({});
        };
        return new Promise(impl);
        return {
          promise: () => new Promise(impl),
        };
      });

    initDateMs = 1610056620001;
    clock = sinon.useFakeTimers({
      now: initDateMs,
    });

    const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 19);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.8.8',
        timezone,
        locale: 'en',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({
        fcmToken: '0',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 0)
      .send({ firstName: 'name' });
    expect(res.status).to.equal(200);

    stub.resetHistory();

    // 8 more days - second flash sale
    clock.tick(8 * 24 * msPerHour);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.premiumFlashSaleReason).to.equal('second');

    // monthly recurring sale
    clock.tick(31 * 24 * msPerHour);

    // notification
    res = await request(app)
      .post('/v1/worker/recurringMonthlySale')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(1);
    expect(stub.callCount).to.equal(1);
    expect(stub.getCall(0).args[0].Template).to.equal('flash-sale-v6');
    reset();
    stub.resetHistory();

    // get flash sale
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.premiumFlashSale.discount).to.equal(50);

    user = await User.findById('0');
    expect(user.events.recurring_monthly_sale).to.equal(1);
    expect(user.premiumFlashSaleReason).to.equal('recurring_monthly_sale');
  } finally {
    clock.restore();
  }
});

it('monthly recurring flash sale, on users with same deviceId APP-488', async () => {
  try {
    const stub = sinon.stub(ses, 'sendTemplatedEmail')
      .callsFake((params) => {
        console.log('Fake sendTemplatedEmail', JSON.stringify(params));
        const impl = function (resolve, reject) {
          resolve({});
        };
        return new Promise(impl);
        return {
          promise: () => new Promise(impl),
        };
      });

    initDateMs = 1610056620001;
    clock = sinon.useFakeTimers({
      now: initDateMs,
    });

    const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 19);

    for (let i = 0; i < 4; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({
          appVersion: '1.8.8',
          timezone,
          locale: 'en',
          os: 'ios',
          deviceId: 'same-device-id'
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', i)
        .send({
          fcmToken: `${i}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', i)
        .send({ firstName: 'name' });
      expect(res.status).to.equal(200);

      stub.resetHistory();

      // 8 more days - second flash sale
      clock.tick(8 * 24 * msPerHour);
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i);
      expect(res.status).to.equal(200);

      user = await User.findById(i);
      if(i === 0){
        expect(user.premiumFlashSaleReason).to.equal('second');
      }else{
        expect(user.premiumFlashSaleReason).to.be.undefined;
      }


      // monthly recurring sale
      clock.tick(31 * 24 * msPerHour);

      // notification
      res = await request(app)
        .post('/v1/worker/recurringMonthlySale')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      if(i === 0){
        expect(notifs.numSent).to.equal(1);
        expect(notifs.recent.token).to.equal('0');
        expect(stub.callCount).to.equal(1);
        expect(stub.getCall(0).args[0].Template).to.equal('flash-sale-v6');
      }else{
        expect(notifs.numSent).to.equal(0);
        expect(stub.callCount).to.equal(0);
      }



      reset();
      stub.resetHistory();

      // get flash sale
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      user = await User.findById(i);
      if(i === 0){
        expect(res.body.user.premiumFlashSale.discount).to.equal(50);
        expect(user.events.recurring_monthly_sale).to.equal(1);
        expect(user.premiumFlashSaleReason).to.equal('recurring_monthly_sale');
      }else{
        expect(res.body.user.premiumFlashSale).to.be.undefined;
        expect(user.events.recurring_monthly_sale).to.equal(0);
        expect(user.premiumFlashSaleReason).to.be.undefined
      }

      if(i === 0){
        // user 0 made purchase
        res = await request(app)
          .put('/v1/user/purchasePremium')
          .set('authorization', i)
          .send({
            receipt: validAppleReceipt,
          });
        expect(res.status).to.equal(200);
      }
    }
  } finally {
    clock.restore();
  }
});

it('notifyFlashSaleExpiration', async () => {
  try {
    clock = sinon.useFakeTimers();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.12',
        locale: 'en',
      });
    expect(res.status).to.equal(200);

    /*
    user = await User.findById('0');
    expect(user.events.notify_flash_sale_expiration).to.equal(0);
    */

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({
        fcmToken: '0',
      });
    expect(res.status).to.equal(200);

    // no notification yet
    res = await request(app)
      .post('/v1/worker/notifyFlashSaleExpiration')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);
    reset();

    /*
    user = await User.findById('0');
    expect(user.events.notify_flash_sale_expiration).to.equal(0);
    */

    // 5 hours, 30 minutes pass
    clock.tick(5.5 * msPerHour);

    // notification
    res = await request(app)
      .post('/v1/worker/notifyFlashSaleExpiration')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.notification.title).to.equal('30 Minutes Left!');
    expect(notifs.recent.notification.body).to.equal('Get 50% off Boo Infinity. Receive up to 3x more matches, and find your best match faster.');
    expect(notifs.recent.data.premiumPopup).to.equal('soulmate');
    expect(notifs.recent.token).to.eql('0');
    reset();

    /*
    user = await User.findById('0');
    expect(user.events.notify_flash_sale_expiration).to.equal(1);
    */

    // another minute passes
    clock.tick(60000);

    // no more notification
    res = await request(app)
      .post('/v1/worker/notifyFlashSaleExpiration')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);

    /*
    user = await User.findById('0');
    expect(user.events.notify_flash_sale_expiration).to.equal(1);
    */

    reset();
  } finally {
    clock.restore();
  }
});

it('notifyFlashSaleExpiration on users with same deviceId APP-488', async () => {
  try {
    clock = sinon.useFakeTimers();

    for (let i = 0; i < 4; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({
          appVersion: '1.13.12',os: 'ios',locale: 'en',deviceId: 'same-device-id'
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', i)
        .send({
          fcmToken: `${i}`,
        });
      expect(res.status).to.equal(200);

      // no notification yet
      res = await request(app)
        .post('/v1/worker/notifyFlashSaleExpiration')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      expect(notifs.numSent).to.equal(0);
      reset();

      // 5 hours, 30 minutes pass
      clock.tick(5.5 * msPerHour);

      // notification
      res = await request(app)
        .post('/v1/worker/notifyFlashSaleExpiration')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      if(i === 0){
        expect(notifs.numSent).to.equal(1);
        expect(notifs.recent.notification.title).to.equal('30 Minutes Left!');
        expect(notifs.recent.notification.body).to.equal('Get 50% off Boo Infinity. Receive up to 3x more matches, and find your best match faster.');
        expect(notifs.recent.data.premiumPopup).to.equal('soulmate');
        expect(notifs.recent.token).to.eql(`${i}`);
      }else{
        expect(notifs.numSent).to.equal(0);
      }

      reset();

      // another minute passes
      clock.tick(60000);

      // no more notification
      res = await request(app)
        .post('/v1/worker/notifyFlashSaleExpiration')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      expect(notifs.numSent).to.equal(0);

      if(i === 0){
        // user 0 made purchase
        res = await request(app)
          .put('/v1/user/purchasePremium')
          .set('authorization', i)
          .send({
            receipt: validAppleReceipt,
          });
        expect(res.status).to.equal(200);
      }
      reset();
    }
  } finally {
    clock.restore();
  }
});

it('delete users', async () => {
  for (let i = 0; i < 3; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', i)
      .send({ firstName: 'name' });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', i)
      .send({
        year: 2000,
        month: 5,
        day: 4,
      });
    expect(res.status).to.equal(200);
  }

  user = await User.findOne({ _id: 0 });
  user.deletionRequestDate = new Date(2000, 1, 1);
  await user.save();
  user = await User.findOne({ _id: 2 });
  user.deletionRequestDate = new Date(2000, 1, 1);
  await user.save();

  res = await request(app)
    .post('/v1/worker/deleteUsers')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  users = await User.find();
  expect(users.length).to.equal(1);
  expect(users[0]._id).to.equal('1');

  users = await mongoose.model('DeletedAccount').find();
  expect(users.length).to.equal(2);
  console.log(users[0]);
  expect(users[0].firstName).to.equal();
  expect(users[0].birthday).to.equal();
  expect(users[0].horoscope).to.equal('Taurus');

  expect(getDeletedUids()).to.eql(['0', '2']);//delete firebase called for the users
});

it('delete users 15 day grace period', async () => {
  // add toFake to avoid the test getting stuck due to the use of 'bottleneck' library in deleteUsers
  clock = sinon.useFakeTimers({toFake: ['Date']});

  const ipBrazil = '************';
  const ipJapan = '************';

  // user 0 - brazil new version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .set('X-Forwarded-For', ipBrazil)
    .send({ appVersion: '1.13.85' })
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.account_deletion_grace_period).to.equal(15);

  res = await request(app)
    .post('/v1/user/accountDeletion')
    .set('authorization', 0)
    .send({ reason: [1] });
  expect(res.status).to.equal(200);

  // user 1 - brazil old version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .set('X-Forwarded-For', ipBrazil)
    .send({ appVersion: '1.13.80' })
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.account_deletion_grace_period).to.equal(30);

  res = await request(app)
    .post('/v1/user/accountDeletion')
    .set('authorization', 1)
    .send({ reason: [1] });
  expect(res.status).to.equal(200);

  // user 2 - japan new version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 2)
    .set('X-Forwarded-For', ipJapan)
    .send({ appVersion: '1.13.85' })
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.account_deletion_grace_period).to.equal(30);

  res = await request(app)
    .post('/v1/user/accountDeletion')
    .set('authorization', 2)
    .send({ reason: [1] });
  expect(res.status).to.equal(200);

  // after 1 day, no users should be deleted
  clock.tick(1 * 24 * msPerHour);
  res = await request(app)
    .post('/v1/worker/deleteUsers')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  users = await User.find().sort('_id');
  expect(users.length).to.equal(3);

  // after 16 days, only user 0 should be deleted
  clock.tick(15 * 24 * msPerHour);
  res = await request(app)
    .post('/v1/worker/deleteUsers')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  users = await User.find().sort('_id');
  expect(users.length).to.equal(2);
  expect(users[0]._id).to.equal('1');
  expect(users[1]._id).to.equal('2');

  // after 31 days, all users should be deleted
  clock.tick(15 * 24 * msPerHour);
  res = await request(app)
    .post('/v1/worker/deleteUsers')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  users = await User.find().sort('_id');
  expect(users.length).to.equal(0);
});

it('num users per language', async () => {
  for (let i = 0; i < 3; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/languages')
    .set('authorization', 0)
    .send({ languages: ['de', 'zh'] });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/languages')
    .set('authorization', 1)
    .send({ languages: ['de', 'en'] });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/languages')
    .set('authorization', 2)
    .send({ languages: ['fr'] });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/worker/recordLanguageMetrics')
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/web/numUsersPerLanguage')
  expect(res.status).to.equal(200);
  expect(res.body.numUsersPerLanguage).to.eql({ fr: 1, zh: 1, de: 2, en: 1 });

  res = await request(app)
    .put('/v1/user/languages')
    .set('authorization', 2)
    .send({ languages: ['haw', 'zh'] });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/worker/recordLanguageMetrics')
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/web/numUsersPerLanguage')
  expect(res.status).to.equal(200);
  expect(res.body.numUsersPerLanguage).to.eql({ haw: 1, zh: 2, de: 2, en: 1 });
});

it('record metrics', async () => {
  for (let i = 0; i < 4; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);
  }

  user = await User.findOne({ _id: 1 });
  user.metrics.lastSeen = Date.now() - 2 * 24 * 3600 * 1000;
  await user.save();

  user = await User.findOne({ _id: 2 });
  user.metrics.lastSeen = Date.now() - 8 * 24 * 3600 * 1000;
  await user.save();

  user = await User.findOne({ _id: 3 });
  user.metrics.lastSeen = Date.now() - 31 * 24 * 3600 * 1000;
  await user.save();

  res = await request(app)
    .post('/v1/worker/recordMetrics')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  metric = await Metric.find();
  expect(metric.length).to.equal(1);
  expect(metric[0].DAU).to.equal(1);
  expect(metric[0].WAU).to.equal(2);
  expect(metric[0].MAU).to.equal(3);
});

it('popular languages', async () => {
  sinon.stub(constants, 'getPopularLanguageDailyPostsThreshold').returns(1);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'chess',
      title: 'title',
      language: 'en',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'chess',
      title: 'title',
      language: 'es',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/worker/recordMetrics')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  metric = await Metric.find();
  expect(metric.length).to.equal(1);
  expect(metric[0].popularLanguages.length).to.equal(2);
  expect(metric[0].popularLanguages).to.have.members(['en', 'es']);

  await metricsLib.loadMostRecentMetrics();
  metricsLib.getPopularLanguages.restore();

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.popular_languages.length).to.equal(2);
  expect(res.body.popular_languages).to.have.members(['en', 'es']);
});

it('inactive_keep_it_up', async () => {
  reset();

  const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 20);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ timezone, locale: 'en' });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/fcmToken')
    .set('authorization', 0)
    .send({
      fcmToken: '0',
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/firstName')
    .set('authorization', 0)
    .send({ firstName: 'name0' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/worker/inactiveReminder')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(notifs.numSent).to.equal(0);

  user = await User.findOne({ _id: '0' });
  user.metrics.lastSeen = Date.now() - 2 * 24 * 3600 * 1000;
  await user.save();

  res = await request(app)
    .post('/v1/worker/inactiveReminder')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(notifs.numSent).to.equal(1);
  expect(notifs.recent.notification.title).to.equal("Keep it up name0!");
  expect(notifs.recent.notification.body).to.equal("New souls join every day.");
  expect(notifs.recent.data).to.eql();
  expect(notifs.recent.token).to.eql('0');
  reset();

  user = await User.findOne({ _id: '0' });
  user.metrics.lastSeen = Date.now() - 3 * 24 * 3600 * 1000;
  await user.save();

  res = await request(app)
    .post('/v1/worker/inactiveReminder')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(notifs.numSent).to.equal(0);
  reset();

  user = await User.findOne({ _id: '0' });
  user.metrics.lastSeen = Date.now() - 4 * 24 * 3600 * 1000;
  await user.save();

  res = await request(app)
    .post('/v1/worker/inactiveReminder')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(notifs.numSent).to.equal(1);
  expect(notifs.recent.notification.title).to.equal("You've been away for a while");
  expect(notifs.recent.notification.body).to.equal("Log back in everyday to continue to be shown to more souls.");
  expect(notifs.recent.data).to.eql();
  expect(notifs.recent.token).to.eql('0');
  reset();
});

describe('email/sms notifications', async () => {
  const smsSuffix = ' ✨ https://my.boo.world/open\n\nReply STOP to unsubscribe.';
  let emailStub; let smsStub; let user; let notificationSettings;

  const testUSPhoneNum = '+16505557777';
  const testNonUSPhoneNum = '+85261236123';
  const user0Lastseen = Date.now() - 50 * 3600 * 1000; // 50 hour ago

  beforeEach(async () => {
    emailStub = sinon.stub(ses, 'sendTemplatedEmail')
      .callsFake((params) => {
        console.log('Fake sendTemplatedEmail', JSON.stringify(params));
        const impl = function (resolve, reject) {
          resolve({});
        };
        return new Promise(impl);
        return {
          promise: () => new Promise(impl),
        };
      });
    smsStub = sinon.stub(fakeTwilioClient.messages, 'create').callsFake(async (params) => {
      console.log('fake sms called', JSON.stringify(params));
      return true;
    });

    const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 13);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        timezone,
      });

    // get user default notification settings
    notificationSettings = res.body.user.pushNotificationSettings;
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 0)
      .send({ firstName: 'name' });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: '0' });
    user.metrics.lastSeen = user0Lastseen;
    user.phoneNumber = testUSPhoneNum;
    await user.save();

    emailStub.resetHistory();
    smsStub.resetHistory();
  });

  afterEach(async () => {
    emailStub.restore();
    smsStub.restore();
  });

  async function removeEmail() {
    user.email = null;
    await user.save();
  }

  async function restoreEmail() {
    user.email = '<EMAIL>';
    await user.save();
  }

  it('no notification email/sms sent', async () => {
    res = await request(app)
      .post('/v1/worker/sendEmailNotifications')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    assert(emailStub.notCalled);
    assert(smsStub.notCalled);

    await removeEmail();

    res = await request(app)
      .post('/v1/worker/sendEmailNotifications')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    assert(emailStub.notCalled);
    assert(smsStub.notCalled);
  });

  describe('new message', async () => {
    beforeEach(async () => {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 1);
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', 1)
        .send({ firstName: 'partner name' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/user/picture/v2')
        .set('authorization', 1)
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);

      res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', 0)
        .send({
          user: '1',
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .patch('/v1/user/approve')
        .set('authorization', 1)
        .send({
          user: '0',
        });
      expect(res.status).to.equal(200);

      emailStub.resetHistory();
    });

    it('new message v5, sms v1', async () => {
      user = await User.findOne({ _id: '0' });
      await user.save();

      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', 1)
        .send({ gender: 'female' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/message')
        .set('authorization', 1)
        .send({
          user: '0',
          text: 'reply',
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      const partner_image = res.body.user.pictures[0];

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      expect(emailStub.callCount).to.equal(1);
      expect(emailStub.getCall(0).args[0].Template).to.equal('new-message-v6');
      assert(smsStub.notCalled);

      notificationSettings.email = false;
      await setNotificationSettings(0, notificationSettings);

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.calledOnce);
      assert(smsStub.calledWith({
        from: twilioNumber,
        to: testUSPhoneNum,
        body: `[Boo] name, you have a new message! See who.${smsSuffix}`,
      }));

      notificationSettings.email = true;
      await setNotificationSettings(0, notificationSettings);
      await removeEmail();

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.calledOnce);
      assert(smsStub.calledWith({
        from: twilioNumber,
        to: testUSPhoneNum,
        body: `[Boo] name, you have a new message! See who.${smsSuffix}`,
      }));

      notificationSettings.sms = false;
      await setNotificationSettings(0, notificationSettings);

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.calledOnce);
      assert(smsStub.calledTwice);
    });

    it('new match v5, sms v1', async () => {
      user = await User.findOne({ _id: '0' });
      await user.save();

      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', 1)
        .send({ gender: 'female' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      const partner_image = res.body.user.pictures[0];

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      expect(emailStub.callCount).to.equal(1);
      expect(emailStub.getCall(0).args[0].Template).to.equal('new-match-v6');
      assert(smsStub.notCalled);

      notificationSettings.email = false;
      await setNotificationSettings(0, notificationSettings);

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.calledOnce);
      assert(smsStub.calledWith({
        from: twilioNumber,
        to: testUSPhoneNum,
        body: `[Boo] name, you have a new match! See who.${smsSuffix}`,
      }));

      notificationSettings.email = true;
      await setNotificationSettings(0, notificationSettings);
      await removeEmail();

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.calledOnce);
      assert(smsStub.calledWith({
        from: twilioNumber,
        to: testUSPhoneNum,
        body: `[Boo] name, you have a new match! See who.${smsSuffix}`,
      }));

      notificationSettings.sms = false;
      await setNotificationSettings(0, notificationSettings);
      assert(emailStub.calledOnce);
      assert(smsStub.calledTwice);
    });

    it('message & match before last seen v5, sms v1', async () => {
      user = await User.findOne({ _id: '0' });
      await user.save();

      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', 1)
        .send({ gender: 'female' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      const partner_image = res.body.user.pictures[0];

      // set lastMessageTime before user lastSeen
      chat = await Chat.findOne({ users: '1', lastMessage: { $exists: false }})
      chat.lastMessageTime = user0Lastseen - 1 * 3600 * 1000; // 1 hour before user 0 lastSeen
      chat.save()

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.notCalled);
      assert(smsStub.notCalled);
    });

    it('not eligible - banned', async () => {
      user = await User.findOne({ _id: '0' });
      user.shadowBanned = true;
      await user.save();

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.notCalled);
      assert(smsStub.notCalled);

      await removeEmail();
      assert(emailStub.notCalled);
      assert(smsStub.notCalled);
    });

    it('eligibility - lastSeen', async () => {
      user = await User.findOne({ _id: '0' });

      user.metrics.lastSeen = Date.now() - 24 * 3600 * 1000;
      await user.save();

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      assert(emailStub.notCalled);
      assert(smsStub.notCalled);

      await removeEmail();
      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      assert(emailStub.notCalled);
      assert(smsStub.notCalled);

      await restoreEmail();

      user.metrics.lastSeen = Date.now() - (2 * 24) * 3600 * 1000;
      await user.save();

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      assert(emailStub.calledOnce);
      assert(smsStub.notCalled);

      await removeEmail();
      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      assert(emailStub.calledOnce);
      assert(smsStub.calledOnce);

      emailStub.resetHistory();
      smsStub.resetHistory();
      await restoreEmail();

      user.metrics.lastSeen = Date.now() - (3 * 24) * 3600 * 1000;
      await user.save();

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.notCalled);

      await removeEmail();
      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.notCalled);
      assert(smsStub.notCalled);

      await restoreEmail();

      user.metrics.lastSeen = Date.now() - (4 * 24) * 3600 * 1000;
      await user.save();

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      assert(emailStub.calledOnce);
      assert(smsStub.notCalled);

      await removeEmail();
      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      assert(emailStub.calledOnce);
      assert(smsStub.calledOnce);

      emailStub.resetHistory();
      smsStub.resetHistory();
      await restoreEmail();

      user.metrics.lastSeen = Date.now() - (5 * 24) * 3600 * 1000;
      await user.save();

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      assert(emailStub.notCalled);
      assert(smsStub.notCalled);

      await removeEmail();
      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      assert(emailStub.notCalled);
      assert(smsStub.notCalled);

      await restoreEmail();
      user.metrics.lastSeen = Date.now() - (6 * 24) * 3600 * 1000;
      await user.save();

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      assert(emailStub.notCalled);
      assert(smsStub.notCalled);

      await removeEmail();
      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      assert(emailStub.notCalled);
      assert(smsStub.notCalled);
    });

    it('not eligible - timezone', async () => {
      const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 16);
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 0)
        .send({
          timezone,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.notCalled);
      assert(smsStub.notCalled);

      await removeEmail();
      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.notCalled);
      assert(smsStub.notCalled);
    });

    it('not eligible - email', async () => {
      user = await User.findOne({ _id: '0' });
      user.email = undefined;
      await user.save();

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.notCalled);
    });

    it('not eligible - sms', async () => {
      user.phoneNumber = null;
      await user.save();

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(smsStub.notCalled);

      user.email = '<EMAIL>';
      await user.save();

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(smsStub.notCalled);

      const notiSettings = (await initApp(0)).user.pushNotificationSettings;
      notiSettings.sms = false;

      await setNotificationSettings(0, notiSettings);

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(smsStub.notCalled);

      user.phoneNumber = testNonUSPhoneNum;
      user.email = null;
      await user.save();
      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(smsStub.notCalled);
    });

    it('not eligible - message seen', async () => {
      res = await request(app)
        .post('/v1/message')
        .set('authorization', 1)
        .send({
          user: '0',
          text: 'reply',
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/message/seen')
        .set('authorization', 0)
        .query({ user: '1' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.notCalled);
      assert(smsStub.notCalled);

      await removeEmail();
      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.notCalled);
      assert(smsStub.notCalled);
    });
  });

  describe('new match', async () => {
    beforeEach(async () => {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 1);
      expect(res.status).to.equal(200);

      res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', 1)
        .send({
          user: '0',
        });
      expect(res.status).to.equal(200);
    });

    it('new like v5, sms v1', async () => {
      user = await User.findOne({ _id: '0' });
      await user.save();

      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', 1)
        .send({ gender: 'female' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      expect(emailStub.callCount).to.equal(1);
      expect(emailStub.getCall(0).args[0].Template).to.equal('new-like-friends-v6');
      assert(smsStub.notCalled);

      notificationSettings.email = false;
      await setNotificationSettings(0, notificationSettings);
      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.calledOnce);
      assert(smsStub.calledWith({
        body: `[Boo] name, you have a new like! See who.${smsSuffix}`,
        from: twilioNumber,
        to: testUSPhoneNum,
      }));

      notificationSettings.email = true;
      await setNotificationSettings(0, notificationSettings);
      await removeEmail();

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.calledOnce);
      assert(smsStub.calledWith({
        body: `[Boo] name, you have a new like! See who.${smsSuffix}`,
        from: twilioNumber,
        to: testUSPhoneNum,
      }));

      notificationSettings.sms = false;
      await setNotificationSettings(0, notificationSettings);

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.calledOnce);
      assert(smsStub.calledTwice);
    });

    it('like before last seen v5, sms v1', async () => {
      userId = '0'
      user = await User.findOne({ _id: userId });
      await user.save();

      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', 1)
        .send({ gender: 'female' });
      expect(res.status).to.equal(200);

      pendingChat = await Chat.findOne({users: userId, pendingUser: userId})
      pendingChat.lastMessageTime = user0Lastseen - 1 * 3600 * 1000; // 1 hour before user 0 lastSeen
      pendingChat.save()

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);



      assert(emailStub.notCalled);
      assert(smsStub.notCalled);
    });

    it('not eligible - seen', async () => {
      res = await request(app)
        .put('/v1/message/seen')
        .set('authorization', 0)
        .query({ user: '1' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.notCalled);
      assert(smsStub.notCalled);

      await removeEmail();
      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.notCalled);
      assert(smsStub.notCalled);
    });
  });

  describe('new recommendations', async () => {
    beforeEach(async () => {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 1);
      expect(res.status).to.equal(200);

      for (let uid = 0; uid < 2; uid++) {
        res = await request(app)
          .put('/v1/user/personality')
          .set('authorization', uid)
          .send({
            mbti: 'ESTJ',
          });
        expect(res.status).to.equal(200);
        res = await request(app)
          .put('/v1/user/gender')
          .set('authorization', uid)
          .send({ gender: 'female' });
        expect(res.status).to.equal(200);
        res = await request(app)
          .patch('/v1/user/preferences')
          .set('authorization', uid)
          .send({
            friends: ['female'],
            personality: ['ESTJ'],
          });
        expect(res.status).to.equal(200);
        res = await request(app)
          .put('/v1/user/birthday')
          .set('authorization', uid)
          .send({
            year: new Date().getFullYear() - 31,
            month: 1,
            day: 1,
          });
        expect(res.status).to.equal(200);
        // Honolulu, HI
        res = await request(app)
          .put('/v1/user/location')
          .set('authorization', uid)
          .send({
            latitude: 21.30,
            longitude: -157.85,
          });
        expect(res.status).to.equal(200);
        // mock upload two pictures
        const user = await User.findOne({ _id: uid });
        user.pictures.push('picture0');
        user.pictures.push('picture1');
        user.viewableInDailyProfiles = true;
        res = await user.save();
      }
      await User.ensureIndexes();
    });

    it('basic', async () => {
      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      expect(emailStub.callCount).to.equal(1);
      expect(emailStub.getCall(0).args[0].Template).to.equal('new-recommendations-v6');

      notificationSettings.email = false;
      await setNotificationSettings(0, notificationSettings);

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.calledOnce);
    });

    it('not eligible - does not fit preferences', async () => {
      res = await request(app)
        .put('/v1/user/personality')
        .set('authorization', 1)
        .send({
          mbti: 'ISTJ',
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.notCalled);
    });

    it('not eligible - already liked', async () => {
      res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', 0)
        .send({
          user: '1',
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      assert(emailStub.notCalled);
    });

    it('new match higher priority than new recs', async () => {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 2);
      expect(res.status).to.equal(200);

      res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', 2)
        .send({
          user: '0',
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      expect(emailStub.callCount).to.equal(1);
      expect(emailStub.getCall(0).args[0].Template).to.equal('new-like-friends-v6');
    });

    it('not translated', async () => {
      user = await User.findById('0');
      user.locale = 'haw';
      await user.save();

      res = await request(app)
        .post('/v1/worker/sendEmailNotifications')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      expect(emailStub.callCount).to.equal(0);
    });
  });

  it('7-day inactive v2', async () => {
    user = await User.findOne({ _id: '0' });
    user.metrics.lastSeen = Date.now() - 7 * 24 * 3600 * 1000;
    await user.save();

    res = await request(app)
      .post('/v1/worker/sendEmailNotifications')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    expect(emailStub.callCount).to.equal(1);
    expect(emailStub.getCall(0).args[0].Template).to.equal('inactive-v6');

    notificationSettings.email = false;
    await setNotificationSettings(0, notificationSettings);

    res = await request(app)
      .post('/v1/worker/sendEmailNotifications')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    assert(emailStub.calledOnce);
  });

  it('25-day deleted account v2', async () => {
    user = await User.findOne({ _id: '0' });
    user.deletionRequestDate = Date.now() - 7 * 24 * 3600 * 1000;
    await user.save();

    res = await request(app)
      .post('/v1/worker/sendEmailNotifications')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    assert(emailStub.notCalled);

    user = await User.findOne({ _id: '0' });
    user.deletionRequestDate = Date.now() - 25 * 24 * 3600 * 1000;
    await user.save();

    res = await request(app)
      .post('/v1/worker/sendEmailNotifications')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    expect(emailStub.callCount).to.equal(1);
    expect(emailStub.getCall(0).args[0].Template).to.equal('delete-account-v6');

    notificationSettings.email = false;
    await setNotificationSettings(0, notificationSettings);

    res = await request(app)
      .post('/v1/worker/sendEmailNotifications')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    assert(emailStub.calledOnce);
  });
});

describe('welcome and tip emails', async () => {
  let stub; let
    timezone;

  beforeEach(async () => {
    stub = sinon.stub(ses, 'sendTemplatedEmail')
      .callsFake((params) => {
        console.log('Fake sendTemplatedEmail', JSON.stringify(params));
        const impl = function (resolve, reject) {
          resolve({});
        };
        return new Promise(impl);
        return {
          promise: () => new Promise(impl),
        };
      });

    timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 14);
  });

  afterEach(async () => {
    stub.restore();
  });

  it('welcome and tip emails', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        timezone,
        locale: 'en',
      });
    expect(res.status).to.equal(200);

    const notificationSettings = res.body.user.pushNotificationSettings;

    notificationSettings.email = false;
    await setNotificationSettings(0, notificationSettings);

    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 0)
      .send({ firstName: 'name' });
    expect(res.status).to.equal(200);

    assert(stub.notCalled);

    notificationSettings.email = true;
    await setNotificationSettings(0, notificationSettings);

    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 0)
      .send({ firstName: 'name' });
    expect(res.status).to.equal(200);

    expect(stub.getCall(0).args[0].Template).to.equal('welcome-v6');
  });

  it('do not send welcome email for non-english locale', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        timezone,
        locale: 'es',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 0)
      .send({ firstName: 'name' });
    expect(res.status).to.equal(200);

    assert(stub.notCalled);

    // complete signup, then change locale, then change name
    res = await request(app)
      .patch('/v1/user/events')
      .set('authorization', 0)
      .send({ finished_signup: true })
    expect(res.status).to.equal(200)

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        locale: 'en',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 0)
      .send({ firstName: 'name2' });
    expect(res.status).to.equal(200);

    assert(stub.notCalled);
  });

  it('no email provided', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        timezone,
      });
    expect(res.status).to.equal(200);
    expect(stub.callCount).to.equal(0);

    user = await User.findOne({ _id: '0' });
    user.email = null;
    await user.save();

    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 0)
      .send({ firstName: 'name' });
    expect(res.status).to.equal(200);
    expect(stub.callCount).to.equal(0);

    res = await request(app)
      .post('/v1/worker/sendEmailNotifications')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(stub.callCount).to.equal(0);
  });

  it('not translated', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        timezone,
      });
    expect(res.status).to.equal(200);
    expect(stub.callCount).to.equal(0);

    user = await User.findOne({ _id: '0' });
    user.locale = 'haw';
    await user.save();

    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 0)
      .send({ firstName: 'name' });
    expect(res.status).to.equal(200);
    expect(stub.callCount).to.equal(0);

    res = await request(app)
      .post('/v1/worker/sendEmailNotifications')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(stub.callCount).to.equal(0);
  });

  /*
  it('web_flash_sale_email_v3 true', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.8.8',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.metrics.numFlashSaleEmailsSent).to.equal();
    user.config.web_flash_sale_email_v3 = true;
    await user.save();

    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 0)
      .send({ firstName: 'name' });
    expect(res.status).to.equal(200);

    expect(stub.callCount).to.equal(2);
    expect(stub.getCall(1).args[0].template_name).to.equal('flash-sale-v4-1');
    user = await User.findById('0');
    expect(user.metrics.numFlashSaleEmailsSent).to.equal(1);
  });

  it('web_flash_sale_email_v3 true but no sale', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.metrics.numFlashSaleEmailsSent).to.equal();
    user.config.web_flash_sale_email_v3 = true;
    await user.save();

    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 0)
      .send({ firstName: 'name' });
    expect(res.status).to.equal(200);

    assert(!stub.calledWith({
      template_name: 'flash-sale-v3',
      template_content: [],
      message: {
        to: [
          {
            email: '<EMAIL>',
          },
        ],
        global_merge_vars: [
          {
            name: 'FNAME',
            content: 'name',
          },
          {
            name: 'USER_IMAGE',
            content: 'MOCK_IMAGE_DOMAIN/undefined',
          },
        ],
        inline_css: true,
        from_email: '<EMAIL>',
        from_name: 'Boo',
        subject: '*|FNAME|*, get 50% off Boo Infinity! ✨',
      },
    }));
    user = await User.findById('0');
    expect(user.metrics.numFlashSaleEmailsSent).to.equal();
  });

  it('web_flash_sale_email_v3 false', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.8.8',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 0)
      .send({ firstName: 'name' });
    expect(res.status).to.equal(200);

    assert(!stub.calledWith({
      template_name: 'flash-sale-v3',
      template_content: [],
      message: {
        to: [
          {
            email: '<EMAIL>',
          },
        ],
        global_merge_vars: [
          {
            name: 'FNAME',
            content: 'name',
          },
          {
            name: 'USER_IMAGE',
            content: 'MOCK_IMAGE_DOMAIN/undefined',
          },
        ],
        inline_css: true,
        from_email: '<EMAIL>',
        from_name: 'Boo',
        subject: '*|FNAME|*, get 50% off Boo Infinity! ✨',
      },
    }));
    user = await User.findById('0');
    expect(user.metrics.numFlashSaleEmailsSent).to.equal(1);
  });
  */
});

describe('daily digest emails', async () => {
  let stub; let
    timezone;

  beforeEach(async () => {
    stub = sinon.stub(ses, 'sendBulkTemplatedEmail')
      .callsFake((params) => {
        console.log('Fake sendBulkTemplatedEmail', JSON.stringify(params));
        const impl = function (resolve, reject) {
          resolve({});
        };
        return new Promise(impl);
        return {
          promise: () => new Promise(impl),
        };
      });

    timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 21);
  });

  afterEach(async () => {
    stub.restore();
  });

  it('daily digest emails', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        timezone,
      });

    const notificationSettings = res.body.user.pushNotificationSettings;

    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 0)
      .send({ firstName: '0' });
    expect(res.status).to.equal(200);

    // create question of the day
    await socialLib.createQuestion({
      text: 'text0',
      interestName: 'questions',
    });

    // create user 1 and post questions
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', 1)
      .send({ firstName: '1' });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 1)
      .send({
        year: new Date().getFullYear() - 31,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', 1)
      .send({
        mbti: 'ESTJ',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    expect(res.body.pictures.length).to.equal(1);
    pictureId = res.body.pictures[0];

    for (let i = 1; i < 5; i++) {
      await socialLib.createQuestion({
        createdBy: '1',
        title: `title${i}`,
        text: `text${i}`,
        interestName: 'kpop',
        score: 100 - i,
      });
      await socialLib.createQuestion({
        createdBy: '1',
        title: 'haw',
        interestName: 'kpop',
        language: 'haw',
      });
      await socialLib.createQuestion({
        createdBy: '1',
        title: 'es',
        interestName: 'kpop',
        language: 'es',
      });
    }

    // daily digest email not sent because user recently active
    stub.resetHistory();
    res = await request(app)
      .post('/v1/worker/dailyDigest')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(stub.callCount).to.equal(0);

    // make user inactive for 1 day
    user = await User.findOne({ _id: 0 });
    user.metrics.lastSeen = Date.now() - 24 * 3600 * 1000;
    await user.save();

    notificationSettings.email = false;
    await setNotificationSettings(0, notificationSettings);

    res = await request(app)
      .post('/v1/worker/dailyDigest')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(stub.callCount).to.equal(0);

    notificationSettings.email = true;
    await setNotificationSettings(0, notificationSettings);

    // set locale to un-translated locale - not sent
    user = await User.findById('0');
    user.locale = 'haw';
    await user.save();

    res = await request(app)
      .post('/v1/worker/dailyDigest')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(stub.callCount).to.equal(0);

    // set locale to english - sent
    user = await User.findById('0');
    user.locale = 'en';
    await user.save();

    stub.resetHistory();
    res = await request(app)
      .post('/v1/worker/dailyDigest')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(stub.callCount).to.equal(1);

    args = stub.getCall(0).args[0];
    expect(args.Template).to.equal('daily-digest-v6');

    // set locale to translated locale - sent
    user = await User.findById('0');
    user.locale = 'es';
    await user.save();

    stub.resetHistory();
    res = await request(app)
      .post('/v1/worker/dailyDigest')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(stub.callCount).to.equal(1);
    args = stub.getCall(0).args[0];
    expect(args.Template).to.equal('daily-digest-v6');

    // make user inactive for 5 days
    user = await User.findOne({ _id: 0 });
    user.metrics.lastSeen = Date.now() - 5 * 24 * 3600 * 1000;
    await user.save();

    // sent
    stub.resetHistory();
    res = await request(app)
      .post('/v1/worker/dailyDigest')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(stub.callCount).to.equal(1);

    // make user inactive for 10 days
    user = await User.findOne({ _id: 0 });
    user.metrics.lastSeen = Date.now() - 10 * 24 * 3600 * 1000;
    await user.save();

    // not sent
    stub.resetHistory();
    res = await request(app)
      .post('/v1/worker/dailyDigest')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(stub.callCount).to.equal(0);

    // change timezone, make inactive for 5 days
    timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 18);
    user = await User.findOne({ _id: 0 });
    user.metrics.lastSeen = Date.now() - 5 * 24 * 3600 * 1000;
    user.timezone = timezone;
    await user.save();

    // not sent
    stub.resetHistory();
    res = await request(app)
      .post('/v1/worker/dailyDigest')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(stub.callCount).to.equal(0);
  });
});

describe('qod push', async () => {
  beforeEach(async () => {
    // create question of the day
    await socialLib.createQuestion({
      text: 'text0',
      interestName: 'questions',
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({
        fcmToken: '0',
      });
    expect(res.status).to.equal(200);

    reset();
  });

  it('qod push en', async () => {
    // not in the right timezone - no notif
    res = await request(app)
      .post('/v1/worker/dailyPush')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);

    // set timezone
    const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 9);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        timezone,
      });
    expect(res.status).to.equal(200);

    // send first question-of-the-day, should be sent
    res = await request(app)
      .post('/v1/worker/dailyPush')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.notification.title).to.equal('Question of the Day');
    expect(notifs.recent.notification.body).to.equal('text0');
    expect(notifs.recent.fcmOptions.analyticsLabel).to.equal('question-of-the-day');
    expect(JSON.parse(notifs.recent.data.question).interestName).to.equal('questions');
    reset();

  });

  it('qod push en, new notification setting questionOfTheDay true', async () => {
    // not in the right timezone - no notif
    res = await request(app)
      .post('/v1/worker/dailyPush')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);

    // set timezone
    const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 9);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.71',
        timezone,
      });
    expect(res.status).to.equal(200);

    user = await User.findOne({_id: 0})
    user.pushNotificationSettings.dailyPush = false
    await user.save()
    console.log('user config :', user.config)

    // send first question-of-the-day, should be sent
    res = await request(app)
      .post('/v1/worker/dailyPush')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.notification.title).to.equal('Question of the Day');
    expect(notifs.recent.notification.body).to.equal('text0');
    expect(notifs.recent.fcmOptions.analyticsLabel).to.equal('question-of-the-day');
    expect(JSON.parse(notifs.recent.data.question).interestName).to.equal('questions');
    reset();

  });

  it('qod push en, new notification setting questionOfTheDay false', async () => {
    // not in the right timezone - no notif
    res = await request(app)
      .post('/v1/worker/dailyPush')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);

    // set timezone
    const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 9);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.71',
        timezone,
      });
    expect(res.status).to.equal(200);

    user = await User.findOne({_id: 0})
    user.pushNotificationSettings.questionOfTheDay = false
    await user.save()
    console.log('user config :', user.config)

    // send first question-of-the-day, should be sent
    res = await request(app)
      .post('/v1/worker/dailyPush')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);
    reset();

  });

  it('qod in other language', async () => {
    // set timezone and locale
    const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 9);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        timezone,
        locale: 'ja',
      });
    expect(res.status).to.equal(200);

    // no push since there is no japanese qod
    res = await request(app)
      .post('/v1/worker/dailyPush')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);

    // create japanese question of the day
    await socialLib.createQuestion({
      text: 'ja',
      interestName: 'questions',
      language: 'ja',
    });

    // should receive push for japanese qod
    res = await request(app)
      .post('/v1/worker/dailyPush')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.notification.body).to.equal('ja');
  });

});

async function teleportToDistance(userId, mockLoc, maxDis, minDis) {
  const randomDistantLocation = generateRandomLocation(
    parseFloat(mockLoc.latitude),
    parseFloat(mockLoc.longitude),
    maxDis,
    minDis,
  );
  await setLocation(userId, [randomDistantLocation.newLongitude, randomDistantLocation.newLatitude]);
}

describe('record city metrics', () => {
  let cities;
  let userDistanceThreshold;
  let cityStub;
  let thresholdDistanceStub;

  beforeEach(() => {
    cities = [];
    userDistanceThreshold = cityLib.getCityUserDistanceThreshold();

    cityStub = sinon.stub(cityLib, 'getCities').callsFake(() => cities);

    thresholdDistanceStub = sinon.stub(cityLib, 'getCityUserDistanceThreshold').callsFake(() => userDistanceThreshold);
  });

  afterEach(() => {
    cityStub.restore();
    thresholdDistanceStub.restore();
  });

  function getGeoPoint(latitude, longitude) {
    return {
      type: 'Point',
      coordinates: [
        parseFloat(longitude),
        parseFloat(latitude),
      ],
    };
  }

  function verifyLocationDoc(locDoc, mockLoc, counts) {
    expect(locDoc.city).to.eql(mockLoc.city);
    expect(locDoc.state).to.eql(mockLoc.state);
    expect(locDoc.country).to.eql(mockLoc.country);
    expect(locDoc.timezone).to.eql(mockLoc.timezone);
    expect(locDoc.location).to.eql(getGeoPoint(mockLoc.latitude, mockLoc.longitude));
    expect(locDoc.userCount).to.eql(counts[0]);
    expect(locDoc.lastCount).to.eql(counts[1]);
  }

  it('generate location stat from cities dump', async () => {
    // create user 0
    await initApp(0);

    // no documents initially
    let locDocs = await LocationStat.find();
    expect(locDocs.length).to.eql(0);

    res = await recordCityMetrics(0);

    // no documents because cities is empty array
    locDocs = await LocationStat.find();
    expect(locDocs.length).to.eql(0);

    cities.push(mockLocs[0]);
    res = await recordCityMetrics(0);

    locDocs = await LocationStat.find();
    expect(locDocs.length).to.eql(1);
    verifyLocationDoc(locDocs[0], mockLocs[0], [0, 0]);

    /// /user 0 teleports within distance 150-200 m from mockLocs[0]
    await teleportToDistance(0, mockLocs[0], 200, 150);

    // set distance threshold to 100 m
    userDistanceThreshold = 100;

    // update city stats again
    res = await recordCityMetrics(0);

    locDocs = await LocationStat.find({ city: { $exists: true } });
    expect(locDocs.length).to.eql(1);
    verifyLocationDoc(locDocs[0], mockLocs[0], [0, 0]);

    // set distance threshold to 200 m
    userDistanceThreshold = 200;

    // update city stats again
    res = await recordCityMetrics(0);

    locDocs = await LocationStat.find({ city: { $exists: true } });
    expect(locDocs.length).to.eql(1);
    verifyLocationDoc(locDocs[0], mockLocs[0], [1, 0]);

    /// /user 0 teleports within distance 150-200 m from mockLocs[1]
    await teleportToDistance(0, mockLocs[1], 200, 150);

    res = await recordCityMetrics(0);

    // last count is not updated on updating existing
    locDocs = await LocationStat.find({ city: { $exists: true } });
    expect(locDocs.length).to.eql(1);
    verifyLocationDoc(locDocs[0], mockLocs[0], [0, 0]);

    cities.push(mockLocs[1]);

    res = await recordCityMetrics(0);

    // last count set when new location is created
    locDocs = await LocationStat.find({ city: { $exists: true } });
    expect(locDocs.length).to.eql(2);
    verifyLocationDoc(locDocs[0], mockLocs[0], [0, 0]);
    verifyLocationDoc(locDocs[1], mockLocs[1], [1, 1]);
  });
});

describe('new users in city', async () => {
  const NOTIFICAION_HOUR = 13;
  let cities;
  let cityStub;
  let notificationStub;
  let hrTimeZonesStub;
  let userDistanceThreshold;
  let userThreshold;

  const mockTzData = {
    currentHour: 0,
    valid: [],
    invalid: [],
  };

  function verifyValidNotification(notifyParams) {
    const { token } = notifyParams;
    const { title } = notifyParams.notification;
    const { body } = notifyParams.notification;
    expect(title).to.eql('Boo');
    expect(body).to.eql(`user_${token}, there has been an increase in souls nearby! See who.`);

  }

  let notifiedTokens = [];

  async function resetNotificationHistory() {

    notificationStub.resetHistory();
    notifiedTokens = [];
    await User.updateMany({}, { $unset: { 'metrics.lastNewJoinNotifiedAt': 0 } });
  }

  async function initialiseRecipientUser(userId) {
    await initApp(userId);
    await setFcmToken(userId, `${userId}`);
    user = await User.findOne({_id: userId})
    console.log('user pushNotificationSettings:', user.pushNotificationSettings)
    await setFirstName(userId, `user_${userId}`);
  }

  beforeEach(async () => {
    cities = mockLocs;
    userDistanceThreshold = cityLib.getCityUserDistanceThreshold();
    userThreshold = cityLib.getCityUserThreshold();

    notificationStub = sinon.stub(fakeAdminMessaging, 'sendEach').callsFake(async (messages) => {
      console.log(`Fake messaging().sendEach(), ${messages.length} messages`);
      const responses = messages.map(message => {
        console.log(`Fake messaging().sendEach()`, message);
        verifyValidNotification(message);
        notifiedTokens.push(message.token);
        return { success: true, messageId: 'msg' };
      });
      return {
        successCount: responses.length,
        failureCount: 0,
        responses
      };
    });

    cityStub = sinon.stub(cityLib, 'getCities').callsFake(() => {
      console.log('city stub called');
      return cities;
    });

    thresholdDistanceStub = sinon.stub(cityLib, 'getCityUserDistanceThreshold').callsFake(() => userDistanceThreshold);

    cityUserThresholdStub = sinon.stub(cityLib, 'getCityUserThreshold').callsFake(() => userThreshold);

    hrTimeZonesStub = sinon.stub(cityLib, 'getTimeZones').callsFake((hr) => ((hr == mockTzData.currentHour) ? mockTzData.valid : mockTzData.invalid));

    await recordCityMetrics(0);
  });

  async function verifyLocDocCounts(mockLoc, userCount, lastCount) {
    const locDoc = await LocationStat.findOne({ city: mockLoc.city, state: mockLoc.state });
    expect(locDoc.userCount).to.eql(userCount);
    expect(locDoc.lastCount).to.eql(lastCount);
  }

  let eventMap = {};
  function triggerEventForUsers(userIds) { userIds.forEach((userId) => { eventMap[userId] = (eventMap[userId] || 0) + 1; }); }

  function verifyNotificationTokens(tokens) {
    console.log('notifiedTokens :',notifiedTokens);
    // sendEach is called once per batch, not once per token
    expect(notificationStub.callCount).to.be.greaterThan(0);
    expect(notifiedTokens.length).to.eql(tokens.length);
    tokens.forEach((token) => expect(notifiedTokens).includes(token));
  }

  async function verifyEventData(userId) {
    const userData = await User.findOne({ _id: userId }, { 'events.newUsersJoinedCity': 1 });
    expect(userData.events.newUsersJoinedCity).to.eql(eventMap[userId] || 0);
  }

  async function verifyUsersEventData(userCount) {
    const tests = [];
    for (let userId = 0; userId <= userCount; userId++) {
      tests.push(verifyEventData(userId));
    }
    await Promise.all(tests);
  }

  afterEach(() => {
    notificationStub.restore();
    cityStub.restore();
    thresholdDistanceStub.restore();
    cityUserThresholdStub.restore();
    hrTimeZonesStub.restore();
    notifiedTokens = []
    eventMap = {}
  });

  it('push notifications for new users in city', async () => {
    await initialiseRecipientUser(0);

    await verifyLocDocCounts(mockLocs[0], 0, 0);
    await verifyLocDocCounts(mockLocs[1], 0, 0);
    await teleportToDistance(0, mockLocs[0], 80, 0);// move within 0-80 m range of loc0

    userDistanceThreshold = 100;
    userThreshold = 2;

    await recordCityMetrics(0);
    // new users updated in locstat
    await verifyLocDocCounts(mockLocs[0], 1, 0);

    // set mockLocs to have timezone
    mockTzData.currentHour = NOTIFICAION_HOUR;
    mockTzData.valid = [mockLocs[0].timezone, mockLocs[1].timezone];

    await cityNewUserPush(0);
    // because the difference < threshold
    assert(notificationStub.notCalled);

    userThreshold = 1;
    await cityNewUserPush(0);
    verifyNotificationTokens(['0']);
    triggerEventForUsers([0]);
    await verifyUsersEventData(0);
    // lastCount updated in locstat
    await verifyLocDocCounts(mockLocs[0], 1, 1);

    await resetNotificationHistory();

    // new user 1
    await initApp(1);
    await setFcmToken(1, '1');

    await teleportToDistance(1, mockLocs[0], 80, 0);
    await recordCityMetrics(0);
    // userCount updated in locstat
    await verifyLocDocCounts(mockLocs[0], 2, 1);

    await cityNewUserPush(0);
    // notification not sent to users with no first name
    verifyNotificationTokens(['0']);
    await verifyLocDocCounts(mockLocs[0], 2, 2);

    triggerEventForUsers([0]);
    await verifyUsersEventData(1);

    await resetNotificationHistory();

    // set firstName for 1
    await setFirstName(1, 'user_1');

    // new user 2
    await initialiseRecipientUser(2);
    await teleportToDistance(2, mockLocs[0], 80, 0);

    await recordCityMetrics(0);
    await verifyLocDocCounts(mockLocs[0], 3, 2);

    await cityNewUserPush(0);
    verifyNotificationTokens(['0', '1', '2']);// now user 1 also receives notification
    await verifyLocDocCounts(mockLocs[0], 3, 3);// last sent count updated in locstat

    triggerEventForUsers([0, 1, 2]);
    await verifyUsersEventData(2);

    await resetNotificationHistory();

    await initialiseRecipientUser(3);// new user 3
    await teleportToDistance(3, mockLocs[0], 80, 0);

    await recordCityMetrics(0);
    await verifyLocDocCounts(mockLocs[0], 4, 3);// new users updated in locstat

    await teleportToDistance(2, mockLocs[0], 200, 150);// user 2 now moves out of threshold distance

    // set timezone current hour != NOTIFICATION_HOUR
    mockTzData.currentHour = NOTIFICAION_HOUR - 1;
    await cityNewUserPush(0);
    assert(notificationStub.notCalled);// because timezone is not having NOTIFICATION_HOUR as current time
    await verifyLocDocCounts(mockLocs[0], 4, 3);// new users not updated in locstat because the location is not processed

    mockTzData.currentHour = NOTIFICAION_HOUR;// set current hour as NOTIFICATION_HOUR
    await cityNewUserPush(0);
    verifyNotificationTokens(['0', '1', '3']);// user 2 does not receive notification as out of threshold distance
    await verifyLocDocCounts(mockLocs[0], 4, 4);// last sent count updated in locstat

    triggerEventForUsers([0, 1, 3]);
    await verifyUsersEventData(3);

    await resetNotificationHistory();
    await teleportToDistance(2, mockLocs[0], 80, 0);// user 2 now moves back in range of threshold distance

    await initialiseRecipientUser(4);// create user 4
    await teleportToDistance(4, mockLocs[0], 80, 0);
    await initialiseRecipientUser(5);// create user 5
    await teleportToDistance(5, mockLocs[1], 80, 0);

    await verifyUsersEventData(5);// to check event data 0 at the start

    await recordCityMetrics(0);
    // new users updated in locstat
    await verifyLocDocCounts(mockLocs[0], 5, 4);
    await verifyLocDocCounts(mockLocs[1], 1, 0);

    mockTzData.valid = [mockLocs[1].timezone];

    await cityNewUserPush(0);
    verifyNotificationTokens(['5']);// only users in valid timezone receive notification
    // only valid timezone locations updated
    await verifyLocDocCounts(mockLocs[0], 5, 4);
    await verifyLocDocCounts(mockLocs[1], 1, 1);// last sent count updated in locstat

    triggerEventForUsers([5]);
    await verifyUsersEventData(5);

    await resetNotificationHistory();

    mockTzData.valid.push(mockLocs[0].timezone);// add loc0 to valid timezones
    // disable push notifications for user 3
    await setNotificationSettings(3, { newSoulsNearby: false });
    await cityNewUserPush(0);
    verifyNotificationTokens(['0', '1', '2', '4']);// user 3 does not receive notification
    await verifyLocDocCounts(mockLocs[0], 5, 5);// last sent count updated in locstat

    triggerEventForUsers([0, 1, 2, 4]);
    await verifyUsersEventData(5);

    await resetNotificationHistory();

    await initialiseRecipientUser(6);// create user 4
    await teleportToDistance(6, mockLocs[0], 80, 0);// user 2 now moves out of threshold distance

    await recordCityMetrics(0);
    await verifyLocDocCounts(mockLocs[0], 6, 5);// new users updated in locstat

    // re-enable notifications
    await setNotificationSettings(3, { newSoulsNearby: true });
    await cityNewUserPush(0);
    verifyNotificationTokens(['0', '1', '2', '3', '4', '6']);// mockLoc1 users don't receive notification as new users are 0 i.e. less than threshold
    await verifyLocDocCounts(mockLocs[0], 6, 6);// last sent count updated in locstat

    triggerEventForUsers([0, 1, 2, 3, 4, 6]);
    await verifyUsersEventData(6);

    await resetNotificationHistory();

    await initialiseRecipientUser(7);// create user 7
    await teleportToDistance(7, mockLocs[1], 80, 0);

    await recordCityMetrics(0);
    await verifyLocDocCounts(mockLocs[1], 2, 1);// new users updated in locstat

    {
      const timeStart = Date.now();

      await cityNewUserPush(0);
      verifyNotificationTokens(['5', '7']);
      await verifyLocDocCounts(mockLocs[1], 2, 2);// last sent count updated in locstat

      triggerEventForUsers([5, 7]);// user 5 triggered event because eligible for notification
      await verifyUsersEventData(7);

      let users = await User.find({ 'metrics.lastNewJoinNotifiedAt': { $gt: timeStart, $lt: Date.now() } });//Metrics.lastNewJoinNotifiedAt updated
      expect(users.length).to.eql(2);
      expect(users.filter((user) => ['5', '7'].includes(user._id)).length).to.eql(2);
    }
    await resetNotificationHistory();

    await initialiseRecipientUser(8);// create user 8
    await teleportToDistance(8, mockLocs[1], 80, 0);

    await recordCityMetrics(0);
    const user8 = await User.findOne({ _id: '8' }, { 'metrics.lastNewJoinNotifiedAt': 1 });//Metrics.lastNewJoinNotifiedAt
    expect(user8.metrics.lastNewJoinNotifiedAt).to.eql(undefined);//metrics uninitialised at the start

    await verifyLocDocCounts(mockLocs[1], 3, 2);// new users updated in locstat

    await User.updateOne({ _id: 5 }, { $set: { 'metrics.lastNewJoinNotifiedAt': DateTime.utc().minus({ hours: 24 * 3 * 1.1 }).toJSDate() } });//interval > 3days
    await User.updateOne({ _id: 7 }, { $set: { 'metrics.lastNewJoinNotifiedAt': DateTime.utc().minus({ hours: 24 * 3 * 0.9 }).toJSDate() } });//interval < 3days
    await User.updateOne({ _id: 8 }, { $set: { 'metrics.lastNewJoinNotifiedAt': DateTime.utc().minus({ hours: 24 * 3 * 1.1 }).toJSDate() } });//interval < 3days
    {
      const timeStart = Date.now();

      await cityNewUserPush(0);
      let users = await User.find({ 'metrics.lastNewJoinNotifiedAt': { $gt: timeStart, $lt: Date.now() } });//Metrics.lastNewJoinNotifiedAtupdated on notification sent/ user eligible
      expect(users.length).to.eql(2);
      expect(users.filter((user) => ['5', '8'].includes(user._id)).length).to.eql(2);

      verifyNotificationTokens(['5', '8']);// user 7 does not receive notifications because 3days > the user cooldown
      await verifyLocDocCounts(mockLocs[1], 3, 3);// last sent count updated in locstat
      triggerEventForUsers([5, 8]);// user 5 triggered event because eligible for notification
      await verifyUsersEventData(8);
    }
  });
});

/*
it('retention metrics - CURR', async function () {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.metrics.activeDates.length).to.equal(1);
  user.metrics.activeDates.push(moment().subtract(8, 'days').toDate());
  await user.save();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  user.metrics.activeDates.push(moment().subtract(9, 'days').toDate());
  user.metrics.lastSeen = moment().subtract(9, 'days').toDate();
  await user.save();

  const numActiveLastWeek = await User.countDocuments(
    {
      'metrics.activeDates': {
        $gt: moment().subtract(14, 'days').toDate(),
        $lt: moment().subtract(7, 'days').toDate(),
      },
    },
  );
  expect(numActiveLastWeek).to.equal(2);

  const numActiveLastWeekAndThisWeek = await User.countDocuments(
    {
      'metrics.activeDates': {
        $gt: moment().subtract(14, 'days').toDate(),
        $lt: moment().subtract(7, 'days').toDate(),
      },
      'metrics.lastSeen': {
        $gt: moment().subtract(7, 'days').toDate(),
      },
    },
  );
  expect(numActiveLastWeekAndThisWeek).to.equal(1);

  res = await request(app)
    .post('/v1/worker/recordMetrics')
  expect(res.status).to.equal(200);

  metric = await Metric.find();
  expect(metric.length).to.equal(1);
  expect(metric[0].CURR).to.equal(0.5);
});

it('retention metrics - RURR', async function () {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 2)
  expect(res.status).to.equal(200);

  user = await User.findById('2');
  user.createdAt = moment().subtract(30, 'days').toDate();
  user.metrics.activeDates.push(moment().subtract(15, 'days').toDate());
  user.metrics.activeDates.push(moment().subtract(9, 'days').toDate());
  user.metrics.lastSeen = moment().subtract(9, 'days').toDate();
  await user.save();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 3)
  expect(res.status).to.equal(200);

  user = await User.findById('3');
  user.createdAt = moment().subtract(30, 'days').toDate();
  user.metrics.activeDates.push(moment().subtract(9, 'days').toDate());
  user.metrics.lastSeen = moment().subtract(9, 'days').toDate();
  await user.save();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 4)
  expect(res.status).to.equal(200);

  user = await User.findById('4');
  user.createdAt = moment().subtract(30, 'days').toDate();
  user.metrics.activeDates.push(moment().subtract(9, 'days').toDate());
  user.metrics.lastSeen = moment().subtract(3, 'days').toDate();
  await user.save();

  const numReactivatedLastWeek = await User.countDocuments(
    {
      'metrics.activeDates': {
        $gt: moment().subtract(14, 'days').toDate(),
        $lt: moment().subtract(7, 'days').toDate(),
        $not: {
          $gt: moment().subtract(21, 'days').toDate(),
          $lt: moment().subtract(14, 'days').toDate(),
        },
      },
      'createdAt': {
        $lt: moment().subtract(21, 'days').toDate(),
      },
    },
  );
  expect(numReactivatedLastWeek).to.equal(2);

  res = await request(app)
    .post('/v1/worker/recordMetrics')
  expect(res.status).to.equal(200);

  metric = await Metric.find();
  expect(metric.length).to.equal(1);
  expect(metric[0].RURR).to.equal(0.5);
});
*/

it('30 day inactive and resurrection', async function () {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.metrics.had30DayInactivity).to.equal(0);
  expect(user.metrics.resurrected).to.equal(0);
  user.metrics.lastSeen = moment().subtract(9, 'days').toDate();
  await user.save();

  res = await request(app)
    .post('/v1/worker/mark30DayInactive')
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.metrics.had30DayInactivity).to.equal(0);
  expect(user.metrics.resurrected).to.equal(0);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.metrics.had30DayInactivity).to.equal(0);
  expect(user.metrics.resurrected).to.equal(0);
  user.metrics.lastSeen = moment().subtract(30, 'days').toDate();
  await user.save();

  res = await request(app)
    .post('/v1/worker/mark30DayInactive')
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.metrics.had30DayInactivity).to.equal(1);
  expect(user.metrics.resurrected).to.equal(0);
  await user.save();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.metrics.had30DayInactivity).to.equal(1);
  expect(user.metrics.resurrected).to.equal(1);
  await user.save();
});

it('att', async () => {
  // user 0: USA yes tracking
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ os: 'ios' })
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 0)
    .send({
      latitude: 21.30,
      longitude: -157.85,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/coins/enableTracking')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  // user 1: USA no tracking
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .send({ os: 'ios' })
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 1)
    .send({
      latitude: 21.30,
      longitude: -157.85,
    });
  expect(res.status).to.equal(200);

  // user 2: UK yes tracking
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 2)
    .send({ os: 'ios' })
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 2)
    .send({
      latitude: 51.75,
      longitude: -1.25,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/coins/enableTracking')
    .set('authorization', 2);
  expect(res.status).to.equal(200);

  // user 3: UK android
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 3)
    .send({ os: 'android' })
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 2)
    .send({
      latitude: 51.75,
      longitude: -1.25,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/worker/recordATT')
  expect(res.status).to.equal(200);

  const att = await ATT.find().sort('-numUsers');
  console.log(att);
  expect(att.length).to.equal(2);
  expect(att[0].country).to.equal('United States');
  expect(att[0].numUsers).to.equal(2);
  expect(att[0].att).to.equal(0.5);
  expect(att[1].country).to.equal('United Kingdom');
  expect(att[1].numUsers).to.equal(1);
  expect(att[1].att).to.equal(1);
});

describe('update cohort cost', () => {
  let reportStub;

  beforeEach(async () => {
    const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 12);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        timezone,
        locale: 'en',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({
        timezone,
        locale: 'en',
      });
    expect(res.status).to.equal(200);

    const reportResult = [
      {
        "cost_date": "2024-02-20",
        "ko_app_id": 67081,
        "ko_app_name": "Boo - Android",
        "company_network_id": 1767,
        "company_network_name": "TikTok",
        "partner_campaign_id": 1766143691249697,
        "partner_campaign_name": "C. Germany - Purchase",
        "adgroup_id": 1766143691249697,
        "adgroup_name": "C | United Kingdom | iOS | Manual | Purchase",
        "country": "",
        "original_currency": "USD",
        "total_impressions": 0,
        "total_clicks": 0,
        "total_installs": 0,
        "calc_total_spend_USD": 0
      },
      {
        "cost_date": "2024-02-20",
        "ko_app_id": 67082,
        "ko_app_name": "Boo - iOS",
        "company_network_id": 1767,
        "company_network_name": "TikTok",
        "partner_campaign_id": 1767390215154690,
        "partner_campaign_name": "C.German - iOS14 || Manual || Purchase",
        "adgroup_id": 1766143691249698,
        "adgroup_name": "C | United Kingdom | iOS | Manual | Purchase",
        "country": "",
        "original_currency": "USD",
        "total_impressions": 0,
        "total_clicks": 0,
        "total_installs": 0,
        "calc_total_spend_USD": 0
      },
      {
        "cost_date": "2024-02-20",
        "ko_app_id": 0,
        "ko_app_name": "",
        "company_network_id": 468,
        "company_network_name": "Apple Search Ads",
        "partner_campaign_id": 1417946541,
        "partner_campaign_name": "Czech_manual",
        "country": "CZ",
        "original_currency": "USD",
        "total_impressions": 0,
        "total_clicks": 0,
        "total_installs": 0,
        "calc_total_spend_USD": 0
      },
      {
        "cost_date": "2024-02-20",
        "ko_app_id": 0,
        "ko_app_name": "",
        "company_network_id": 468,
        "company_network_name": "Apple Search Ads",
        "partner_campaign_id": 1381689998,
        "partner_campaign_name": "Dating",
        "country": "AE,AL,AR,AT,AU,AZ,BE,CA,CH,CL,CO,CZ,DE,DK,EC,EG,ES,FI,FR,GB,GR,HK,HR,HU,ID,IE,IL,IN,IT,JO,JP,KH,KR,KW,KZ,LB,MO,MX,MY,NL,NO,NZ,OM,PE,PH,PK,PL,PT,QA,RO,RU,SA,SE,SG,TH,TW,UA,US,VN,ZA",
        "original_currency": "USD",
        "total_impressions": 0,
        "total_clicks": 0,
        "total_installs": 0,
        "calc_total_spend_USD": 0
      },
      {
        "cost_date": "2024-02-20",
        "ko_app_id": 67081,
        "ko_app_name": "Boo - Android",
        "company_network_id": 1767,
        "company_network_name": "TikTok",
        "partner_campaign_id": 1768870008682497,
        "partner_campaign_name": "Android || Spark Ads || Purchase",
        "adgroup_id": 1766143691249699,
        "adgroup_name": "C | United Kingdom | iOS | Manual | Purchase",
        "country": "",
        "original_currency": "USD",
        "total_impressions": 0,
        "total_clicks": 0,
        "total_installs": 13,
        "calc_total_spend_USD": 22.14
      },
      {
        "cost_date": "2024-02-20",
        "ko_app_id": 0,
        "ko_app_name": "",
        "company_network_id": 468,
        "company_network_name": "Apple Search Ads",
        "partner_campaign_id": 1417929263,
        "partner_campaign_name": "Hungary_manual",
        "country": "HU",
        "original_currency": "USD",
        "total_impressions": 0,
        "total_clicks": 0,
        "total_installs": 0,
        "calc_total_spend_USD": 0
      },
      {
        "cost_date": "2024-02-20",
        "ko_app_id": 0,
        "ko_app_name": "",
        "company_network_id": 468,
        "company_network_name": "Apple Search Ads",
        "partner_campaign_id": 1076421032,
        "partner_campaign_name": "Argentina - Dating",
        "country": "AR",
        "original_currency": "USD",
        "total_impressions": 0,
        "total_clicks": 0,
        "total_installs": 0,
        "calc_total_spend_USD": 0
      },
      {
        "cost_date": "2024-02-20",
        "ko_app_id": 0,
        "ko_app_name": "",
        "company_network_id": 468,
        "company_network_name": "Apple Search Ads",
        "partner_campaign_id": 1507201692,
        "partner_campaign_name": "US Competitors (New)",
        "country": "US",
        "original_currency": "USD",
        "total_impressions": 753,
        "total_clicks": 21,
        "total_installs": 6,
        "calc_total_spend_USD": 39.199
      }
    ];

    reportStub = sinon.stub(kochavaLib, 'getKochavaReport').callsFake((token) => {
      const impl = function (resolve, reject) {
        resolve(reportResult);
      };
      return new Promise(impl);
    });
  });

  afterEach(() => {
    console.log("after each")
    reportStub.restore();
  });

  it('should not affecting unrelated records', async () => {
    clock = sinon.useFakeTimers(Date.UTC(2024, 1, 20));
    clock.tick(24 * msPerHour);

    user = await User.findOne({ _id: '0' });
    user.kochava = {
      partner_campaign_id: 'xxx',
      adCampaignCohortCost: 10
    };
    user.createdAt = Date.UTC(2024, 1, 20, 0, 10, 0, 0);
    await user.save();

    let purchaseReceipt = await PurchaseReceipt({
      user: user._id,
      kochava: {
        partner_campaign_id: 'xxx',
        adCampaignCohortCost: 1
      }
    });
    await purchaseReceipt.save();

    let neureonPurchaseReceipt = await NeureonPurchaseReceipt({
      user: user._id,
      kochava: {
        partner_campaign_id: 'xxx',
        adCampaignCohortCost: 2
      }
    });
    await neureonPurchaseReceipt.save();

    let superLikePurchaseReceipt = await SuperLikePurchaseReceipt({
      user: user._id,
      kochava: {
        partner_campaign_id: 'xxx',
        adCampaignCohortCost: 3
      }
    });
    await superLikePurchaseReceipt.save();

    let coinPurchaseReceipt = await CoinPurchaseReceipt({
      user: user._id,
      kochava: {
        partner_campaign_id: 'xxx',
        adCampaignCohortCost: 4
      }
    });
    await coinPurchaseReceipt.save();

    clock.tick(24 * msPerHour);
    res = await request(app)
      .post('/v1/worker/recordCohortCost')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: '0' });
    expect(user.kochava.adCampaignCohortCost).to.equal(10);

    purchaseReceipt = await PurchaseReceipt.findOne({ user: '0' });
    expect(purchaseReceipt.kochava.adCampaignCohortCost).to.equal(1);

    neureonPurchaseReceipt = await NeureonPurchaseReceipt.findOne({ user: '0' });
    expect(neureonPurchaseReceipt.kochava.adCampaignCohortCost).to.equal(2);

    superLikePurchaseReceipt = await SuperLikePurchaseReceipt.findOne({ user: '0' });
    expect(superLikePurchaseReceipt.kochava.adCampaignCohortCost).to.equal(3);

    coinPurchaseReceipt = await CoinPurchaseReceipt.findOne({ user: '0' });
    expect(coinPurchaseReceipt.kochava.adCampaignCohortCost).to.equal(4);

    resetTime = Date.now();
    clock.restore();
  });

  it('should affecting related records by date and partner campaign id', async () => {
    clock = sinon.useFakeTimers(Date.UTC(2024, 1, 20));
    clock.tick(24 * msPerHour);

    user = await User.findOne({ _id: '0' });
    user.kochava = {
      partner_campaign_id: 1507201692,
      adCampaignCohortCost: 10
    };
    user.createdAt = Date.UTC(2024, 1, 20, 0, 10, 0, 0);
    await user.save();

    const purchaseDate = moment(Date.UTC(2024, 1, 20)).add(1, 'hours');
    let purchaseReceipt = await PurchaseReceipt({
      purchaseDate: purchaseDate,
      user: user._id,
      kochava: {
        partner_campaign_id: 1507201692,
        adCampaignCohortCost: 1
      }
    });
    await purchaseReceipt.save();

    let neureonPurchaseReceipt = await NeureonPurchaseReceipt({
      purchaseDate: purchaseDate,
      user: user._id,
      kochava: {
        partner_campaign_id: 0,
        adCampaignCohortCost: 2
      }
    });
    await neureonPurchaseReceipt.save();

    let superLikePurchaseReceipt = await SuperLikePurchaseReceipt({
      purchaseDate: purchaseDate,
      user: user._id,
      kochava: {
        partner_campaign_id: 0,
        adCampaignCohortCost: 3
      }
    });
    await superLikePurchaseReceipt.save();

    let coinPurchaseReceipt = await CoinPurchaseReceipt({
      purchaseDate: purchaseDate,
      user: user._id,
      kochava: {
        partner_campaign_id: 0,
        adCampaignCohortCost: 4
      }
    });
    await coinPurchaseReceipt.save();

    res = await request(app)
      .post('/v1/worker/recordCohortCost')
      .set('authorization', 0);
    expect(res.status).to.equal(200);


    user = await User.findOne({ _id: '0' });
    expect(user.kochava.adCampaignCohortCost).to.equal(39.199);

    purchaseReceipt = await PurchaseReceipt.findOne({ user: '0' });
    expect(purchaseReceipt.kochava.adCampaignCohortCost).to.equal(39.199);

    neureonPurchaseReceipt = await NeureonPurchaseReceipt.findOne({ user: '0' });
    expect(neureonPurchaseReceipt.kochava.adCampaignCohortCost).to.equal(2);

    superLikePurchaseReceipt = await SuperLikePurchaseReceipt.findOne({ user: '0' });
    expect(superLikePurchaseReceipt.kochava.adCampaignCohortCost).to.equal(3);

    coinPurchaseReceipt = await CoinPurchaseReceipt.findOne({ user: '0' });
    expect(coinPurchaseReceipt.kochava.adCampaignCohortCost).to.equal(4);
    resetTime = Date.now();
    clock.restore();
  });

  it('should affecting related records owned by different person', async () => {
    clock = sinon.useFakeTimers(Date.UTC(2024, 1, 20));
    clock.tick(24 * msPerHour);

    let user1 = await User.findOne({ _id: '0' });
    user1.kochava = {
      partner_campaign_id: 1507201692,
      adCampaignCohortCost: 10
    };
    user1.createdAt = Date.UTC(2024, 1, 20, 0, 10, 0, 0);
    await user1.save();

    let user2 = await User.findOne({ _id: '1' });
    user2.kochava = {
      partner_campaign_id: 1768870008682497,
      adgroup_id: 1766143691249699,
      adCampaignCohortCost: 0
    };
    user2.createdAt = Date.UTC(2024, 1, 20, 0, 15, 0, 0);
    await user2.save();

    const purchaseDate = moment(Date.UTC(2024, 1, 20)).add(1, 'hours');
    let purchaseReceipt = await PurchaseReceipt({
      purchaseDate: purchaseDate,
      user: user1._id,
      kochava: {
        partner_campaign_id: 1507201692,
        adCampaignCohortCost: 1
      }
    });
    await purchaseReceipt.save();

    let neureonPurchaseReceipt = await NeureonPurchaseReceipt({
      purchaseDate: purchaseDate,
      user: user2._id,
      kochava: {
        partner_campaign_id: 0,
        adCampaignCohortCost: 2
      }
    });
    await neureonPurchaseReceipt.save();

    let superLikePurchaseReceipt = await SuperLikePurchaseReceipt({
      purchaseDate: purchaseDate,
      user: user1._id,
      kochava: {
        partner_campaign_id: 0,
        adCampaignCohortCost: 3
      }
    });
    await superLikePurchaseReceipt.save();

    let coinPurchaseReceipt = await CoinPurchaseReceipt({
      purchaseDate: purchaseDate,
      user: user2._id,
      kochava: {
        partner_campaign_id: 1768870008682497,
        adCampaignCohortCost: 4
      }
    });
    await coinPurchaseReceipt.save();

    res = await request(app)
      .post('/v1/worker/recordCohortCost')
      .set('authorization', 0);
    expect(res.status).to.equal(200);


    user1 = await User.findOne({ _id: '0' });
    expect(user1.kochava.adCampaignCohortCost).to.equal(39.199);

    user2 = await User.findOne({ _id: '1' });
    expect(user2.kochava.adCampaignCohortCost).to.equal(22.14);

    purchaseReceipt = await PurchaseReceipt.findOne({ user: '0' });
    expect(purchaseReceipt.kochava.adCampaignCohortCost).to.equal(39.199);

    neureonPurchaseReceipt = await NeureonPurchaseReceipt.findOne({ user: '1' });
    expect(neureonPurchaseReceipt.kochava.adCampaignCohortCost).to.equal(2);

    superLikePurchaseReceipt = await SuperLikePurchaseReceipt.findOne({ user: '0' });
    expect(superLikePurchaseReceipt.kochava.adCampaignCohortCost).to.equal(3);

    coinPurchaseReceipt = await CoinPurchaseReceipt.findOne({ user: '1' });
    expect(coinPurchaseReceipt.kochava.adCampaignCohortCost).to.equal(4);
    resetTime = Date.now();
    clock.restore();
  });
})

describe('app_910: newArrivalsNotification worker', () => {
  let clock = sinon.useFakeTimers({
    now: Date.now(),
    toFake: ['Date'],
  });

  beforeEach(async () => {
    for (let i = 0; i < 3; i++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.13.95' });
      expect(res.status).to.equal(200);

      const user = await User.findById(i.toString());
      user.config.app_910 = true;
      user.fcmToken = `token_${i}`;
      user.pushNotificationSettings.promotions = true;
      await user.save();
    }
    reset();
  });

  afterEach(() => {
    if (clock) {
      clock.restore();
    }
  });

  it('should send notification to users with scheduled notifications', async () => {
    const now = new Date();
    const pastTime = new Date(now.getTime() - 5 * 60 * 1000); // 5 minutes ago

    await User.updateOne(
      { _id: '0' },
      { newArrivalsNotificationScheduledAt: pastTime }
    );

    await User.updateOne(
      { _id: '1' },
      { newArrivalsNotificationScheduledAt: pastTime }
    );

    let user0 = await User.findOne({ _id: '0' }).lean();
    expect(user0.newArrivalsNotificationScheduledAt).to.be.instanceOf(Date);

    let user1 = await User.findOne({ _id: '1' }).lean();
    expect(user1.newArrivalsNotificationScheduledAt).to.be.instanceOf(Date);

    // Run the worker
    let res = await request(app)
      .post('/v1/worker/newArrivalsNotification')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    user0 = await User.findOne({ _id: '0' }).lean()
    expect(user0.newArrivalsNotificationScheduledAt).to.equal();

    user1 = await User.findOne({ _id: '1' }).lean()
    expect(user1.newArrivalsNotificationScheduledAt).to.equal();

    expect(notifs.numSent).to.equal(2);
    expect(notifs.recent.notification.title).to.equal(`New souls just joined in your area!`);
    expect(notifs.recent.notification.body).to.equal(`Don't wait, see who now.`);
    expect(notifs.recent.data.premiumPopup).to.equal('unlimitedLikes');

  });

  it('should not send notification to users without scheduled notifications', async () => {
    let res = await request(app)
      .post('/v1/worker/newArrivalsNotification')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    expect(notifs.numSent).to.equal(0);
  });

  it('should not send notification to users with future scheduled time', async () => {
    // Schedule notification for future
    const futureTime = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes from now

    await User.updateOne(
      { _id: '0' },
      { newArrivalsNotificationScheduledAt: futureTime }
    );

    let res = await request(app)
      .post('/v1/worker/newArrivalsNotification')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    expect(notifs.numSent).to.equal(0);

    const user0 = await User.findById('0');
    expect(user0.newArrivalsNotificationScheduledAt).to.not.equal(undefined);
  });

  it('should increment numNotificationsNewArrivals metric when notification is sent', async () => {
    // Schedule notification for user
    const pastTime = new Date(Date.now() - 5 * 60 * 1000);

    const user = await User.findById('0');
    user.newArrivalsNotificationScheduledAt = pastTime;
    user.metrics.numNotificationsNewArrivals = 0
    await user.save();

    // Run the worker
    let res = await request(app)
      .post('/v1/worker/newArrivalsNotification')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    const updatedUser = await User.findById('0');
    expect(updatedUser.metrics.numNotificationsNewArrivals).to.equal(1);
    expect(updatedUser.newArrivalsNotificationScheduledAt).to.equal(undefined);
    expect(notifs.numSent).to.equal(1);
  });

  it('should handle race conditions with atomic updates', async () => {
    const pastTime = new Date(Date.now() - 5 * 60 * 1000);

    const users = await User.find({ _id: { $in: ['0', '1'] } });

    for (const user of users) {
      user.newArrivalsNotificationScheduledAt = pastTime;
      user.metrics.numNotificationsNewArrivals = 0;
      await user.save();
    }

    const workerPromises = [
      request(app).post('/v1/worker/newArrivalsNotification').set('authorization', 0),
      request(app).post('/v1/worker/newArrivalsNotification').set('authorization', 0),
      request(app).post('/v1/worker/newArrivalsNotification').set('authorization', 0)
    ];

    const results = await Promise.all(workerPromises);

    results.forEach(res => expect(res.status).to.equal(200));

    await new Promise((r) => setTimeout(r, 200));

    const updatedUsers = await User.find({ _id: { $in: ['0', '1'] } });
    for (const user of updatedUsers) {
      expect(user.metrics.numNotificationsNewArrivals).to.equal(1);
      expect(user.newArrivalsNotificationScheduledAt).to.equal(undefined);
    }

    expect(notifs.numSent).to.equal(2);

    let res = await request(app)
      .post('/v1/worker/newArrivalsNotification')
      .set('authorization', 0);
      expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    expect(notifs.numSent).to.equal(2);
  });

  it('should not send notification to users with promotions disabled', async () => {
    const pastTime = new Date(Date.now() - 5 * 60 * 1000);
    const user = await User.findById('0');
    user.pushNotificationSettings.promotions = false;
    user.newArrivalsNotificationScheduledAt = pastTime;
    await user.save();

    let res = await request(app)
      .post('/v1/worker/newArrivalsNotification')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    expect(notifs.numSent).to.equal(0);
  });
});

it('error route', async () => {
  res = await request(app)
    .post('/v1/worker/error');
  expect(res.status).to.equal(500);
})
