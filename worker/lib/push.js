const { DateTime } = require('luxon');
const ct = require('countries-and-timezones');
const { phone } = require('phone');
const { translate, locales } = require('../../lib/translate');
const { sendNotification, MAX_FCM_BATCH_SIZE, endLiveActivityHTTPv1, sendBatchNotifications, generateMessageForNotification } = require('../../config/firebase-admin');
const User = require('../../models/user');
const LocationStat = require('../../models/location-stat');
const chatLib = require('../../lib/chat');
const cityLib = require('../../lib/city');
const profilesLib3 = require('../../lib/profiles-v3');
const scoreLib = require('../../lib/score');
const socialLib = require('../../lib/social');
const { IMAGE_DOMAIN } = require('../../lib/constants');
const { sendFlashSaleEmail, translateEmail, getCommonTranslatedTags, sendEmailTemplate, sendEmailTemplateSES, sendDailyDigestToCursor } = require('../../lib/email');
const emailLib = require('../../lib/email');
const metricsLib = require('../../lib/metrics');
const { limitLikes, getRemainingDailyLimit } = require('../../lib/user');
const premiumLib = require('../../lib/premium');
const coinsLib = require('../../lib/coins');
const coinsConstants = require('../../lib/coins-constants');
const { sendSms } = require('../../lib/twilio-sms');
const { readPreference, replicaTags } = require('../../lib/read-preference-analytics');
const { getTodaysTheme, getAnalyticsLabel, generateAiNotification } = require('./notifications/helper');
const { sendSocketEvent } = require('../../lib/socket');

let newArrivalsNotificationWorkerRunning = false

const INACTIVE_REMINDER_DAYS = [2, 4];

function buildSms(user, notificationType) {
  const body = translate(`{{name}}, you have a new ${notificationType}! See who.`, user.locale, { name: user.firstName });
  return `[Boo] ${body} ✨ https://my.boo.world/open\n\nReply STOP to unsubscribe.`;
}
const smsNotifications = {
  like: (user) => buildSms(user, 'like'),
  message: (user) => buildSms(user, 'message'),
  match: (user) => buildSms(user, 'match'),
};

const datingNotifications = [
  { title: 'Personality-based Dating', body: 'It\'s not about tailoring who you are to attract someone. It\'s having the confidence to just be yourself for the person who will like you for who you naturally are. (they\'re on Boo) Say hi 👋 .' },
  { title: 'Compatible Looks Like', body: 'Someone for whom your weaknesses are tolerable in light of your strengths. Say hi to your compatible match. 😊' },
  { title: 'Peak Enlightenment', body: 'When you finally accept being comfortable with who you naturally are after realizing there are others like you. Even people looking for someone just like you. ❤️ (psst.. they\'re on Boo) Say hi 👋' },
  { title: '', body: 'It can be hard to find something real these days. A deeper connection. Not on Boo, it\'s why we made it. Send a first message 👀 and give real connection a chance.' },
  { title: 'Compatible Looks Like', body: 'Someone who is everything you\'re not, yet somehow just the same. What are you waiting for? Say hi to your compatible match 👋' },
  { title: 'Compatible Looks Like', body: 'Here\'s to the deep conversations, adventures, and feeling genuinely understood and accepted. 🥂 Your compatible match awaits. Send them a message! 😊' },
  { title: 'Compatible Looks Like', body: 'Someone you\'d allow into your inner world, show your art to, and reveal your secret action figures. Say hi to the person you\'d let your inner child play with. 😊' },
  { title: 'Compatible Looks Like', body: 'Someone with whom watching paint dry wouldn\'t be the worst thing in the world. Say hi to your compatible match. 😊' },
  { title: 'Compatible Looks Like', body: 'Someone whom conversation with just feels magical. Say hi to your compatible match. 😊' },
  { title: 'Compatible Looks Like', body: 'Someone who doesn\'t make you feel inadequate, but more than enough. Say hi to your compatible match. 😊' },
  { title: 'Compatible Looks Like', body: 'Someone that inspires you to be better. Say hi to your compatible match. 😊' },
  { title: 'Compatible Looks Like', body: 'Someone who feels like your missing piece, complementing each other\'s strengths and weaknesses. Say hi to your compatible match. 😊' },
  { title: 'Compatible Looks Like', body: 'When you\'re both the most inspiring person in the world to each other. Say hi to your compatible match. 😊' },
  { title: 'Compatible Looks Like', body: 'Someone who helps you become a better version of yourself. Say hi to your compatible match. 👋' },
  { title: 'Compatible Means', body: 'Giving and taking for each other, but never having to compromise the core of who you are. What are you waiting for? Say hi to your compatible match 👋' },
  { title: 'Compatible Looks Like', body: 'Someone who doesn\'t try to force you to change. Someone for whom you\'re already what they\'re looking for. Say hi to your compatible match. 😊' },
  { title: 'Compatible Looks Like', body: 'Ugly crying from the raw release of the best damn conversation you ever had on a dating (and friends) app. Say hi to your compatible match 👋' },
  { title: 'Compatible Looks Like', body: 'The intensely long and deeply honest, authentic, and personal conversations that look like long breakup texts. 📜 Say hi to your compatible match 👋' },
  { title: 'Love isn\'t random', body: 'Once you realize this, dating becomes a lot clearer and easier. Welcome to the new age of personality-enabled dating (+friends).🌹 Your new matches await. Say hi 👋' },
  { title: 'Fun Fact ⭐', body: 'Most fictional love interests nowadays are based on compatible personality. The Mr. Darcy to your Elizabeth Bennett is waiting for your DM. Send them a message. 👀' },
  { title: 'Compatible Means', body: 'Willing to understand, appreciate and respect each other\'s differences. We use personality psychology to make that more likely, but the rest is up to your commitment to making it work. 😉' },
  { title: 'Compatible Looks Like', body: 'The moment you realize you\'re comfortable being your true selves with each other, and nothing changes.' },
  { title: 'Compatible Looks Like', body: 'Being drawn to someone for who they are and what they stand for over just appearances. Your compatible match awaits. Say hi 😊' },
  { title: 'Compatible Looks Like', body: 'It\'s time to take a chance on yourself. Here\'s to the relationships yet to be made. 🥂  Your compatible match awaits. Say hi 😊' },
  { title: 'Compatible Looks Like', body: 'Your inner child playing with their inner child. Say hi to the person whom you\'d let down your walls for. 👋' },
  { title: 'Compatible Looks Like', body: 'Reverting to the five year old versions of yourselves when you\'re with each other. Say hi to the match who makes you feel free to be yourself. 👋' },
  { title: 'Compatible Looks Like', body: 'Being the only two weirdos in the room, but normal in each other\'s eyes. Get weird with your compatible match. 🤪' },
  { title: 'Compatible Looks Like', body: 'Someone, for some inexplicable reason, liking you for who you naturally are. Your compatible match awaits. Say hi 😊' },
  { title: 'Compatible Looks Like', body: 'Feeling like you can be yourself without fear of rejection. Your compatible match awaits. Say hi 😊' },
  { title: 'Compatible Looks Like', body: 'Someone with whom everything just seems easier. Say hi to your compatible match. 😊' },
  { title: 'Compatible Looks Like', body: 'Knowing what your weaknesses are, but still choosing you over any other type. Say hi to the person that wouldn\'t mind as much. 👋 ' },
  { title: 'Compatible Looks Like', body: 'Smiling so hard you feel like an idiot so you try to stop but you just can\'t help it. Say hi to the person that makes you uncontrollably want to smile. 😊😊😊' },
  { title: 'Compatible Looks Like', body: 'Waking up one day and seeing life through rose-tinted glasses and renewed vibrance. Say hi to the person that can help you rediscover life. 😊' },
  { title: 'Compatible Looks Like', body: 'When you catch yourself singing in the shower even though you know you\'re always off-key. Find someone that just makes you want to sing. 😊' },
  { title: 'Compatible Looks Like', body: 'Someone who intuitively understands you and accepts you. Say hi to your compatible match who doesn\'t need further explanation. 👋' },
  { title: '', body: '\'Na na na na na, yeah. You are the music in me.\' 🎶 - Troy and Gabriella, High School Musical. Say hi to the person that could co-star as your musical love interest. 😊' },
  { title: 'Compatible Looks Like', body: 'When the conversation and chemistry are simply, effortless. Say hi to your compatible match. 😊' },
  { title: 'Your Shakespearean Romance', body: 'Romeo was an ENFP, and Juliet an INFP. You could say they were twin flames.🔥 Your passionate Shakespearean romance could be just one DM away. Say hi 👋' },
  { title: 'Your Story 📖', body: 'You were on Boo. And so were they. The rest was history. You know what to do 👀 😉' },
  { title: '', body: 'The gods have spoken. Love (or a new friend) is in your future. 👀🌹' },
  { title: '', body: 'The universe has chosen you for these compatible boos. 👀🙌' },
  { title: '', body: '\'If you know the personalities of your match and yourself, you need not fear the result of a hundred dates.\' - Sun Boo, Art of Boo ⚔️' },
  { title: '', body: '\'Your time is limited, so don\'t waste it dating incompatible personalities.\' - Steve Boo' },
  { title: 'Compatible Looks Like', body: 'Someone who makes you comfortable and appreciated being your most natural self. Say hi to your compatible match. 😊' },
  { title: 'Compatible Looks Like', body: 'Talking for hours but feeling like only minutes have passed. Say hi to your compatible match. 😊' },
  { title: 'Compatible Looks Like', body: 'Sharing the same love languages, giving and feeling loved in the same ways. Say hi to your compatible match. 😊' },
  { title: 'Compatible Looks Like', body: 'Still liking you despite you saying all the wrong things. Find someone that gets you. Say hi to your compatible match. 😊' },
  { title: 'Compatible Looks Like', body: 'Someone both same and different in just the right ways. Say hi to your compatible match. 😊' },
  { title: 'Date like Steve Jobs', body: 'Steve Jobs (ENTJ) once said of his relationship with his wife Laurene (INTJ), it \'just gets better and better as the years roll on.\' Find your partner in crime 🤝 that shares your values. (they\'re on Boo) Say hi! 👋' },
  { title: 'Compatible Looks Like', body: 'When you laugh like Jim and Pam from The Office. Say hi to effortless chemistry and endless inside jokes. 😊' },
  { title: '', body: 'Today\'s forecast: good vibes, great conversation, and ⚡ chemistry. Send them a message.' },
  { title: 'Compatible Looks Like', body: 'A country music song. Here\'s someone you might just want to have a nice cold beer with.🍻 Say hi to your compatible match. 😊' },
  { title: 'New Souls Found 👀', body: 'How compatible? Finish each other\'s sentences, compatible. You know what to do. 😉' },
];

const friendsNotifications = [
  {
    title: 'Friendship Looks Like',
    body: 'Someone who encourages your uniqueness instead of limiting it! Your new friend awaits. Say hi. 😊',
  },
  {
    title: 'Friendship Looks Like',
    body: 'Someone who shares your idea of a good time! Your new friend awaits. Say hi. 😊',
  },
  {
    title: 'Friendship Looks Like',
    body: 'A synchronized dance of conversation and ideas! Say hi to your compatible friend. 😊',
  },
  {
    title: 'Friendship Looks Like',
    body: 'Spontaneous adventures whose memories last a lifetime. Your compatible friend awaits. Say hi. 😊',
  },
  {
    title: 'Friendship Isn\'t Random',
    body: 'That\'s why we found you a compatible new friend. Your new match awaits. Say hi. 😊',
  },
  {
    title: 'Friendship Looks Like',
    body: 'When you strike a balance with each other\'s energy like Michael (ESFP) and Dwight (ESTJ). Say hi to your new partner in crime. 😊',
  },
  {
    title: 'Friendship Looks Like',
    body: 'A thoughtful discussion about personality! Say hi to your compatible friend.',
  },
  {
    title: 'How Do You Know If You\'ve Found A Friend?',
    body: 'Compatibility is a good start! A compatible friend awaits. Say hi. 😊',
  },
  {
    title: 'Friendship Looks Like',
    body: 'Someone who is like the peanut butter to your jelly. Both great individually but iconic together. Say hi to your dangerous duo. 😊',
  },
  {
    title: 'Friendship Looks Like',
    body: 'Someone respectfully agreeing (or disagreeing) with why pineapple does (or doesn\'t) belong on pizza. Your future pizza pal awaits. Say hi. 😊',
  },
  {
    title: 'First Impressions Can Be Scary',
    body: 'Luckily we found you a compatible friend who appreciates you for you. Dont be nervous, your new friend awaits. Say hi. 😊',
  },
  {
    title: 'Friendship Looks Like',
    body: 'Being the only two people dying of laughter in a quiet restaurant. Your new friend awaits. Say hi. 😊',
  },
  {
    title: 'You Know You\'ve Found A Friend When',
    body: 'You both geek out on the same music. Your compatible friends awaits. Say hi. 😊',
  },
  {
    title: 'You Know You\'ve Found A Friend When',
    body: 'You both geek out on the same movies. Your compatible friends awaits. Say hi. 😊',
  },
  {
    title: 'You Know You\'ve Found A Friend When',
    body: 'You both geek out on the same books. Your compatible friends awaits. Say hi. 😊',
  },
  {
    title: 'You Know You\'ve Found A Friend When',
    body: 'You both geek out on the same games. Your compatible friends awaits. Say hi. 😊',
  },
  {
    title: 'Friendship Looks Like',
    body: 'Car concerts with an audience of 2. Your compatible co-star awaits. Say hi. 😊',
  },
  {
    title: 'Roses Are Red, Violets Are Blue',
    body: 'We found you a compatible friend, we hope you like them as much as we do. 😊',
  },
  {
    title: 'Friendship Looks Like',
    body: 'C3P0 and R2-D2 helping each other out of tought spots and having a blast doing it. Say hi to your new partner in crime. 😊',
  },
  {
    title: 'You Know You\'ve Found A Friend When',
    body: 'You can tell them your guilty pleasures without fear of judgement. Your compatible friend awaits. Say hi. 😊',
  },
  {
    title: 'Compatible Friendship Is When',
    body: 'You wonder when you all became comfortable enough with each other to reveal your childhood fears. Your compatible friend awaits. Say hi. 😊',
  },
  {
    title: 'Friendship Looks Like',
    body: 'Each getting the others\' favorite snack before movie night. Your compatible friends awaits. Say hi 😊.',
  },
  {
    title: 'Friendship Looks Like',
    body: 'Having heated debates about your common interests, and then going out to get food like nothing happened. Your dynamic duo awaits. Say hi. 😊',
  },
  {
    title: 'Friendship Looks Like',
    body: 'Being in the same room doing your own thing, just enjoying each other\'s company. Say hi to your compatible friend.',
  },
  {
    title: 'Everyday Is Full Of Possibility',
    body: 'A new (and compatible) friend is right around the corner. Come say hi. 😊',
  },
  {
    title: 'Friendship Looks Like',
    body: 'A charcuterie board, where you each bring something unique to the table and it creates a magical combination. Say hi to your compatible match. 😊',
  },
  {
    title: 'Its Hard To Imagine Harry Potter Without Ron Weasly',
    body: 'You could be a moments away from meeting your future bestie. Your new match awaits. Say hi. 😊',
  },
  {
    title: 'Compatible Looks Like',
    body: 'Someone with whom watching paint dry wouldn\'t be the worst thing in the world. Say hi to your compatible match. 😊',
  },
  {
    title: 'Compatible Looks Like',
    body: 'Someone whom conversation with just feels magical. Say hi to your compatible match. 😊',
  },
  {
    title: 'Compatible Looks Like',
    body: 'Someone who doesn\'t make you feel inadequate, but more than enough. Say hi to your compatible match. 😊',
  },
  {
    title: 'Compatible Looks Like',
    body: 'Someone that inspires you to be better. Say hi to your compatible match. 😊',
  },
  {
    title: 'Compatible Looks Like',
    body: 'The moment you realize you\'re comfortable being your true selves with each other, and nothing changes.',
  },
  {
    title: 'Compatible Looks Like',
    body: 'When the conversation and chemistry are simply, effortless. Say hi to your compatible match. 😊',
  },
  {
    title: 'Compatible Looks Like',
    body: 'Your inner child playing with their inner child. Say hi to the person whom you\'d let down your walls for. 👋',
  },
  {
    title: 'Compatible Looks Like',
    body: 'Reverting to the five year old versions of yourselves when you\'re with each other. Say hi to the match who makes you feel free to be yourself. 👋',
  },
  {
    title: '',
    body: 'Finding new friends is like a cat finding their new favorite nap spot, once you have a few really good ones, you become hard to find. A compatible friend awaits. Say hi.',
  },
  {
    title: '',
    body: 'Naruto might have searched the entire world for Sasuke, but we made Boo so your new best friend could be just a tap away. 😊',
  },
  {
    title: '',
    body: 'Finding the bard to your barbarian can be hard without a bit of luck. That\'s why we found you a compatible friend. Your next party member could be right around the corner. 😊',
  },
  {
    title: '',
    body: 'We might not all be the son of Poseiden like Percy Jackson, but we all have a Grover out there waiting for us. A compatible friend awaits. Say hi. 😊',
  },
  {
    title: '',
    body: 'Like Arya Stark and Jaqen H\'ghar, a good friend is someone you can trust and learn from. A compatible friend is right around the corner, don\'t let them stay faceless for long! 😊',
  },
  {
    title: '',
    body: 'You don\'t need a Mr. Meseeks to find you a compatible friend. We already did that! Your new friend awaits. Say hi. 😊',
  },
  {
    title: '',
    body: 'In the words of Marcus Aurelius, \'Be content with what you are and wish not change\'. A friend who thinks of you the same way is right around the corner. Say hi. 😊',
  },
  {
    title: '',
    body: 'The Agrippa to your Augustus could be right around the corner. A new match awaits you Princep. 😊',
  },
  {
    title: '',
    body: 'Friendship is like taking the perfect photo. It might take a few attempts to get the right one, but once you got it, it lasts a lifetime. A compatible match awaits. Say hi.',
  },
  {
    title: '',
    body: 'I see you\'re enjoying your own company ... it would be a shame if we found you a new compatible friend... whose waiting for you to say hi. 😊',
  },
];

const emailBaseQuery = {
  email: { $ne: null },
  'pushNotificationSettings.email': { $ne: false },
};
const emailBaseProjection = ['email', 'pushNotificationSettings', 'config', 'locale'];


function getTimezones(hour) {
  return cityLib.getTimeZones(hour);
}

function localeNotification(notification, locale, ...subs) {
  return {
    title: translate(notification.title, locale, ...subs),
    body: translate(notification.body, locale, ...subs),
    data: notification.data,
  };
}

async function sendNotificationsWithCursor(matchQuery, notification, analyticsLabel, translateNotif) {
  // filter out users without fcmToken
  matchQuery = {
    ...matchQuery,
    fcmToken: { $ne: null },
  };
  const projection = ['fcmToken'];
  if (translateNotif) {
    const englishNotif = localeNotification(notification, 'en');
    for await (const locale of locales) {
      const translatedNotif = localeNotification(notification, locale);
      if (locale != 'en' && ((translatedNotif.title && translatedNotif.title == englishNotif.title) || (translatedNotif.body && translatedNotif.body == englishNotif.body))) {
        // skip if not translated
        continue;
      }
      matchQuery.locale = locale;
      await sendUntranslatedNotificationsUsingCursor(matchQuery, translatedNotif, analyticsLabel);
    }
  } else {
    await sendUntranslatedNotificationsUsingCursor(matchQuery, notification, analyticsLabel);
  }
}

function getGenderSymbol(gender) {
  return gender == 'female' ? 'g' : 'b';
}

function getGenderTemplate(user, otherUser, templatePrefix) {
  const myGender = getGenderSymbol(user.gender);
  const theirGender = getGenderSymbol(otherUser.gender);
  return `${templatePrefix}-${theirGender}${myGender}`;
}

async function prepareAiNotification(user, theme) {
  const result = await generateAiNotification(user, theme);
  if (result?.output?.length === 2 && result.analyticsLabel) {
    return { user, ...result };
  }
  return null;
}

async function processUserBatch(users, theme) {
  if (!Array.isArray(users) || users.length === 0) return 0;

  try {
    const sendResults = await Promise.allSettled(users.map(user => prepareAiNotification(user, theme)));

    const notificationsToSend = [];
    let notifiedCount = 0;

    for (const result of sendResults) {
      if (result.status === 'fulfilled' && result.value) {
        const { user, output, analyticsLabel } = result.value;
        notificationsToSend.push(sendNotification(
          user,
          null,
          output[0],
          output[1],
          null,
          null,
          'general',
          analyticsLabel,
          null,
          null,
          'numNotificationsDailyAIV2',
        ));
        notifiedCount++;
      }
    }

    if (notificationsToSend.length > 0) {
      await Promise.all(notificationsToSend);
    }

    return notifiedCount;
  } catch (err) {
    console.log('Ai notifications: processUserBatch: Error processing batch:', err);
    return 0;
  }
}

function groupByUserId(items, userField, validUserIds) {
  const grouped = {};

  for (const item of items) {
    let userIds = [];
    if (Array.isArray(item[userField])) {
      userIds = item[userField].map(id => id?.toString());
    } else if (item[userField]) {
      userIds = [item[userField].toString()];
    }

    for (const idStr of userIds) {
      if (validUserIds.includes(idStr)) {
        if (!grouped[idStr]) {
          grouped[idStr] = [];
        }
        grouped[idStr].push(item);
      }
    }
  }

  return grouped;
}

async function sendAiNotifications(query) {
  const date = new Date();
  const formatted = date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  const theme = getTodaysTheme(formatted);
  if (!theme) {
    console.log(`No theme found for today: ${formatted}`);
    return;
  }

  const projection = {
    _id: 1,
    age: 1,
    gender: 1,
    locale: 1,
    'preferences.dating': 1,
    'preferences.friends': 1,
    datingSubPreferences: 1,
    'personality.mbti': 1,
    interestNames: 1,
    fcmToken: 1,
  };

  const cursor = User.find(query, projection).read(readPreference, replicaTags);

  let batch = [];
  let numProcessed = 0;
  let numNotified = 0;

  for await (const user of cursor) {
    batch.push(user.toObject());
    numProcessed++;

    if (batch.length > 25) {
      numNotified += await processUserBatch(batch, theme);
      batch = [];
    }
  }

  if (batch.length > 0) {
    numNotified += await processUserBatch(batch, theme);
  }

  const analyticsLabel = getAnalyticsLabel(theme);
  console.log(`sendAiNotifications: numProcessed: ${numProcessed}, numNotified: ${numNotified}, label: ${analyticsLabel}`);
}

async function sendDailyPushExperiment(query, baselineNotification, analyticsLabelBase) {
  const projection = {
    fcmToken: 1,
    locale: 1,
  };
  const cursor = User.find(query, projection)
                     .read(readPreference, replicaTags)

  let numProcessed = 0;
  let numNotified = 0;
  let promisesArr = [];
  for await (const user of cursor) {
    let analyticsLabel = analyticsLabelBase;
    let notification = baselineNotification;
    numProcessed++;

    const locale = user.locale;
    const englishNotif = localeNotification(notification, 'en');
    const translatedNotif = localeNotification(notification, locale);
    if (locale != 'en' && ((translatedNotif.title && translatedNotif.title == englishNotif.title) || (translatedNotif.body && translatedNotif.body == englishNotif.body))) {
      // skip if not translated
      continue;
    }

    numNotified++;
    promisesArr.push(sendNotification(
      user,
      null,
      notification.title,
      notification.body,
      null,
      null,
      'general',
      analyticsLabel,
      null,
      null,
      'numNotificationsDailyNonAI',
    ));
    if (promisesArr.length > 25) {
      await Promise.all(promisesArr);
      promisesArr = [];
    }
  }
  await Promise.all(promisesArr);
  console.log(`sendDailyPushExperiment: ${analyticsLabelBase}, numProcessed: ${numProcessed}, numNotified: ${numNotified}`);
}

async function dailyPush(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  const oneDayAgo = DateTime.utc().minus({ days: 1 }).toJSDate();
  const daysSinceEpoch = Math.floor(new Date() / 8.64e7);

  const baseQuery = {
    fcmToken: { $ne: null },
    'metrics.lastSeen': { $lt: oneDayAgo },
    timezone: { $in: getTimezones(12) },
    'pushNotificationSettings.dailyFacts': { $ne: false },
  };

  /* APP-782 false
  // send ai notifications
  {
    const query = {
      ...baseQuery,
      signupCountry: 'United States',
      locale: 'en',
      'config.app_782': true,
      'interestNames.4': { $exists: true },
    };
    await sendAiNotifications(query);
  }
  */

  /// /send dating Notification
  {
    const datingQuery = {
      ...baseQuery,
      'preferences.dating.0': { $exists: true },
    };
    const datingIndex = daysSinceEpoch % datingNotifications.length;
    const notification = {
      ...datingNotifications[datingIndex],
      sound: 'general',
    };
    await sendDailyPushExperiment(datingQuery, notification, 'daily-push-dating');
  }

  /// /send friends Notification
  {
    const friendsQuery = {
      ...baseQuery,
      'preferences.dating': { $type: 'array' },
      'preferences.dating.0': { $exists: false },
    };
    const friendsIndex = daysSinceEpoch % friendsNotifications.length;
    const notification = {
      ...friendsNotifications[friendsIndex],
      sound: 'general',
    };
    await sendDailyPushExperiment(friendsQuery, notification, 'daily-push-friends');
  }

  // send Qod Notifications
  for (const locale of locales) {
    const posts = await socialLib.getQuestionFeed({
      sort: 'popular',
      language: locale,
    });
    if (posts.length > 0 && posts[0].interestName == 'questions') {
      const question = posts[0];
      const questionData = {
        _id: question._id,
        interest: null,
        interestName: question.interestName,
      };
      const notification = {
        title: translate('Question of the Day', locale),
        body: question.text,
        data: { question: JSON.stringify(questionData) },
        sound: 'general',
      };
      {
        const query = {
          timezone: { $in: getTimezones(9) },
          locale,
          'pushNotificationSettings.questionOfTheDay': { $ne: false}
        };
        const numNotified = await sendUntranslatedNotificationsUsingCursor(query, notification, 'question-of-the-day');
        console.log(`QOD push for locale ${locale}, num notified: ${numNotified}`);
      }
    }
  }

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function notifyFlashSaleExpiration(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  const query = {
    actualCountry: { $nin: ['Russia'] },
    shadowBanned: { $ne: true },
    'metrics.numPurchases': 0,
    premiumExpiration: null,
    fcmToken: { $ne: null },
    premiumFlashSaleEndDate: {
      $gt: DateTime.utc().plus({ minutes: 29 }).toJSDate(),
      $lte: DateTime.utc().plus({ minutes: 30 }).toJSDate(),
    },
    'pushNotificationSettings.promotions': { $ne: false },
    alreadyPurchasedPremiumOnUserId: null
  };

  {
    const notification = {
      title: '30 Minutes Left!',
      body: 'Get 50%% off Boo Infinity. Receive up to 3x more matches, and find your best match faster.',
      data: {
        "premiumPopup": "soulmate"
      },
      sound: 'general',
    };
    await sendNotificationsWithCursor(query, notification, 'infinity-sale-30-minutes-left', true);
  }

  /*
  await User.updateMany(
    query,
    { $inc: { 'events.notify_flash_sale_expiration': 1 } },
  );
  */

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function updateUserScores(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  await scoreLib.updateUserScores(
    {
      'metrics.lastSeen': {
        $lte: DateTime.utc().minus({ hours: 7 * 24 }).toJSDate(),
        $gte: DateTime.utc().minus({ hours: 7 * 24 + 2 }).toJSDate(),
      },
    },
  );

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function secondSale(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  const baseQuery = {
    actualCountry: { $nin: ['Russia'] },
    shadowBanned: { $ne: true },
    premiumFlashSaleEndDate: { $lt: new Date() },
    'metrics.numPurchases': 0,
    premiumExpiration: null,
    'metrics.numFlashSales': 1,
    'pushNotificationSettings.promotions': { $ne: false },
    createdAt: {
      $lte: DateTime.utc().minus({ hours: 24 * 7 }).toJSDate(),
      $gt: DateTime.utc().minus({ hours: 24 * 7 + 1 }).toJSDate(),
    },
    alreadyPurchasedPremiumOnUserId: null
  };

  { // fcm notification
    const notification = {
      title: '⚡50%% OFF SALE! Last Chance',
      body: 'Level up and save 50%% off Boo Infinity. 6 hours limited time only.',
      sound: 'general',
    };
    await sendNotificationsWithCursor(baseQuery, notification, 'infinity-flash-sale-day-7', true);
  }

  const emailQuery = {
    ...baseQuery,
    ...emailBaseQuery,
  };

  // email Data
  const projection = ['_id', 'firstName', 'pictures'].concat(emailBaseProjection);

  const cursor = User.find(emailQuery, projection)
                     .read(readPreference, replicaTags)

  for await (const user of cursor) {
    await sendFlashSaleEmail(user);
  }

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function superLikeSale(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  {
    const baseQuery = {
      fcmToken: { $ne: null },
      actualCountry: { $nin: ['Russia'] },
      shadowBanned: { $ne: true },
      'metrics.numSuperLikePurchases': 0,
      'pushNotificationSettings.promotions': { $ne: false },
      createdAt: {
        $lte: DateTime.utc().minus({ hours: 24 * 8 }).toJSDate(),
        $gt: DateTime.utc().minus({ hours: 24 * 9 }).toJSDate(),
      },
      timezone: { $in: getTimezones(21) },
    };

    const notification = {
      title: '⚡50%% OFF SUPER LOVES',
      body: 'Save 50%% off Super Loves. 6 hours limited time only.',
      data: {
        superLikeFlashSale: JSON.stringify({
          super_like_discounted_product_ids: [
              'super_love_3_discount_50_v1',
              'super_love_12_discount_50_v1',
              'super_love_50_discount_50_v1',
          ],
          super_like_discount: 50,
        }),
      },
      sound: 'general',
    };
    await sendNotificationsWithCursor(baseQuery, notification, 'super-love-flash-sale', true);
  }

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function coinsSale(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  {
    const baseQuery = {
      fcmToken: { $ne: null },
      actualCountry: { $nin: ['Russia'] },
      shadowBanned: { $ne: true },
      'metrics.numCoinPurchases': 0,
      'pushNotificationSettings.promotions': { $ne: false },
      createdAt: {
        $lte: DateTime.utc().minus({ hours: 24 * 9 }).toJSDate(),
        $gt: DateTime.utc().minus({ hours: 24 * 10 }).toJSDate(),
      },
      timezone: { $in: getTimezones(21) },
    };

    const notification = {
      title: '⚡30%% OFF COINS',
      body: 'Save 30%% off coins. 6 hours limited time only.',
      data: {
        coinsFlashSale: JSON.stringify({}),
      },
      sound: 'general',
    };
    await sendNotificationsWithCursor(baseQuery, notification, 'coins-flash-sale', true);
  }

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function boostPromoD1(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  {
    const day = 1;
    const analyticsLabel = 'boosts-promo-day-1';
    const baseQuery = {
      fcmToken: { $ne: null },
      actualCountry: { $nin: ['Russia'] },
      shadowBanned: { $ne: true },
      'metrics.numBoostPurchases': 0,
      'pushNotificationSettings.promotions': { $ne: false },
      createdAt: {
        $lte: DateTime.utc().minus({ hours: 24 * 1 }).toJSDate(),
        $gt: DateTime.utc().minus({ hours: 24 * 2 }).toJSDate(),
      },
      timezone: { $in: getTimezones(19) },
    };

    const notification = {
      title: 'Use Boost ✨',
      body: 'Get up to 4x more profile views and become the most popular soul in your area.',
      data: {
        openPage: "boost"
      },
      sound: 'general',
    };
    await sendNotificationsWithCursor(baseQuery, notification, analyticsLabel, true);
  }

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function boostSaleD3(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  {
    const analyticsLabel = 'boosts-sale-day-3';
    const now = DateTime.utc().toJSDate();

    const baseQuery = {
      fcmToken: { $ne: null },
      actualCountry: { $nin: ['Russia'] },
      shadowBanned: { $ne: true },
      boostsFlashSaleEndDate: undefined,
      'metrics.numBoostPurchases': 0,
      'pushNotificationSettings.promotions': { $ne: false },
      createdAt: {
        $lte: DateTime.utc().minus({ hours: 24 * 3 }).toJSDate(),
        $gt: DateTime.utc().minus({ hours: 24 * 4 }).toJSDate(),
      },
      timezone: { $in: getTimezones(19) },
      $or: [
        { 'config.app_935': { $ne: true } },
        {
          'config.app_935': true,
          $or: [
            { premiumExpiration: { $gt: now } },
            { premiumV2Expiration: { $gt: now } },
            { godModeExpiration: { $gt: now } },
          ],
        },
      ],
    };

    const notification = {
      title: '⚡50%% OFF BOOSTS',
      body: 'Buy now to save 50%% on Boosts. Limited time offer: 6 hours only.',
      data: {
        openPage: "boost"
      },
      sound: 'general',
    };
    await sendNotificationsWithCursor(baseQuery, notification, analyticsLabel, true);
  }

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function recurringMonthlySale(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  const baseQuery = {
    actualCountry: { $nin: ['Russia'] },
    shadowBanned: { $ne: true },
    'metrics.numPurchases': 0,
    premiumExpiration: null,
    'pushNotificationSettings.promotions': { $ne: false },
    premiumFlashSaleEndDate: {
      $lte: DateTime.utc().minus({ hours: 24 * 30 }).toJSDate(),
    },
    timezone: { $in: getTimezones(19) },
    alreadyPurchasedPremiumOnUserId: null
  };

  { // fcm notification
    const notification = {
      title: '⚡50%% OFF SALE! Last Chance',
      body: 'Level up and save 50%% off Boo Infinity. 6 hours limited time only.',
      sound: 'general',
    };
    await sendNotificationsWithCursor(baseQuery, notification, 'infinity-flash-sale-monthly', true);
  }

  const emailQuery = {
    ...baseQuery,
    ...emailBaseQuery,
  };

  // email Data
  const projection = ['_id', 'firstName', 'pictures'].concat(emailBaseProjection);

  const cursor = User.find(emailQuery, projection)
                     .read(readPreference, replicaTags)

  for await (const user of cursor) {
    await sendFlashSaleEmail(user);
  }

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function day2Push(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  function getNotification(user) {
    const notification = {
      title: 'Patience is key',
      body: 'But Boo Infinity members receive up to 3X more matches and find their best match faster.',
      data: { "premiumPopup": "soulmate" },
    };
    const englishNotif = localeNotification(notification, 'en');
    const translatedNotif = localeNotification(notification, user.locale);
    return { englishNotif, translatedNotif };
  }

  await sendRevenuePushNotifications({
    day: 2,
    timezone: 18,
    getNotification,
    analyticsLabel: 'infinity-day-2-promotion',
  });

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function sendTranslatedNotificationsUsingCursor(cursor, getNotification, analyticsLabel) {
  let promisesArr = [];
  for await (const user of cursor) {
    const { englishNotif, translatedNotif } = getNotification(user);
    if (user.locale != 'en') {
      if (translatedNotif.title == englishNotif.title || translatedNotif.body == englishNotif.body) {
        // skip if not translated
        continue;
      }
    }
    promisesArr.push(sendNotification(
      user,
      null,
      translatedNotif.title,
      translatedNotif.body,
      translatedNotif.data,
      null,
      'general',
      analyticsLabel,
    ));
    if (promisesArr.length > 25) {
      await Promise.all(promisesArr);
      promisesArr = [];
    }
  }
  await Promise.all(promisesArr);
}

async function sendUntranslatedNotificationsUsingCursor(matchQuery, notification, analyticsLabel) {
  // filter out users without fcmToken
  matchQuery = {
    ...matchQuery,
    fcmToken: { $ne: null },
  };
  const projection = ['fcmToken'];
  const cursor = User.find(matchQuery, projection)
                     .read(readPreference, replicaTags)

  let i = 0;
  let promisesArr = [];
  for await (const user of cursor) {
    i++;

    promisesArr.push(sendNotification(
      user,
      null,
      notification.title,
      notification.body,
      notification.data,
      null,
      'general',
      analyticsLabel,
    ));
    if (promisesArr.length > MAX_FCM_BATCH_SIZE) {
      await Promise.all(promisesArr);
      promisesArr = [];
    }
  }
  await Promise.all(promisesArr);
  return i;
}

async function sendRevenuePushNotifications(params) {
  const {
    day,
    timezone,
    getNotification,
    analyticsLabel,
    additionalQueryParams,
  } = params;

  let query = {
    fcmToken: { $ne: null },
    actualCountry: { $nin: ['Russia'] },
    shadowBanned: { $ne: true },
    'metrics.numPurchases': 0,
    premiumExpiration: null,
    createdAt: {
      $lte: DateTime.utc().minus({ hours: 24 * day }).toJSDate(),
      $gt: DateTime.utc().minus({ hours: 24 * (day+1) }).toJSDate(),
    },
    'pushNotificationSettings.promotions': { $ne: false },
    timezone: { $in: getTimezones(timezone) },
  };
  if (additionalQueryParams) {
    query = {
      ...query,
      ...additionalQueryParams,
    };
  }

  const projection = {
    fcmToken: 1,
    locale: 1,
    countryLocale: 1,
  };
  const cursor = User.find(query, projection)
                     .read(readPreference, replicaTags)

  await sendTranslatedNotificationsUsingCursor(cursor, getNotification, analyticsLabel);
}

// note: this function is no longer used
async function day1UniversesPush(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  const notification = {
    title: 'Post in the Universes',
    body: "Souls who post and comment make a lot more connections. Plus it's fun, try it out!",
    data: {
      "openPage": "universes"
    },
  };
  const day = 1;
  const timezone = 15;
  const analyticsLabel = 'day-1-post-in-universes';

  let query = {
    fcmToken: { $ne: null },
    shadowBanned: { $ne: true },
    createdAt: {
      $lte: DateTime.utc().minus({ hours: 24 * day }).toJSDate(),
      $gt: DateTime.utc().minus({ hours: 24 * (day+1) }).toJSDate(),
    },
    'pushNotificationSettings.promotions': { $ne: false },
    timezone: { $in: getTimezones(timezone) },
    'config.day_1_universes_push': true,
    locale: 'en',
  };

  const projection = {
    fcmToken: 1,
  };
  const cursor = User.find(query, projection)
                     .read(readPreference, replicaTags)

  let promisesArr = [];
  for await (const user of cursor) {
    promisesArr.push(sendNotification(
      user,
      null,
      notification.title,
      notification.body,
      notification.data,
      null,
      'general',
      analyticsLabel,
    ));
    if (promisesArr.length > 25) {
      await Promise.all(promisesArr);
      promisesArr = [];
    }
  }
  await Promise.all(promisesArr);

  await User.updateMany(
    query,
    {
      'events.day_1_universes_push': 1,
    },
  );

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function day1SpiritRealmPush(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  function getNotification(user) {
    const notification = {
      title: 'Try the Spirit Realm',
      body: "Activate the ultimate invisibility cloak. See other people, but other people can't see you. 👀",
      data: { premiumPopup: 'spiritRealm' },
    };
    const englishNotif = localeNotification(notification, 'en');
    const translatedNotif = localeNotification(notification, user.locale);
    return { englishNotif, translatedNotif };
  }

  await sendRevenuePushNotifications({
    day: 1,
    timezone: 18,
    getNotification,
    analyticsLabel: 'infinity-day-1-spirit-realm-promotion',
    additionalQueryParams: {
      $or: [
        { hiddenContacts: true },
        { hideFromNearby: true },
        { hideQuestions: true },
        { hideComments: true },
        { hideMyFollowerCount: true },
        { hideMyAwards: true },
        { hideMyKarma: true },
        { hideMyAge: true },
        { hideCity: true },
        { 'hideFromKeywords.0': { $exists: true } },
      ],
    },
  });

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function day1UnlimitedLovesPush(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  function getNotification(user) {
    const numUsersWhoPurchased = metricsLib.getNumUniqueUsersWhoPurchasedInfinity();
    const countryLocale = (user.countryLocale || 'en_US').replace('_', '-');
    let formatted;
    try {
      formatted = new Intl.NumberFormat(countryLocale).format(numUsersWhoPurchased);
    } catch (err) {
      formatted = new Intl.NumberFormat('en-US').format(numUsersWhoPurchased);
    }
    const notification = {
      title: "Don't wait until tomorrow",
      body: 'Join {{number}} souls who have unlocked Boo Infinity and unlimited loves.',
      data: { premiumPopup: 'unlimitedLikes' },
    };
    const englishNotif = localeNotification(notification, 'en', { number: formatted });
    const translatedNotif = localeNotification(notification, user.locale, { number: formatted });
    return { englishNotif, translatedNotif };
  }

  await sendRevenuePushNotifications({
    day: 1,
    timezone: 21,
    getNotification,
    analyticsLabel: 'infinity-day-1-unlimited-loves-promotion',
  });


  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function day3Push(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  function getNotification(user) {
    const notification = {
      title: 'Pro Tip: Send a DM',
      body: 'Stand out with personalized greetings.',
      data: { premiumPopup: 'unlimitedDMs' },
    };
    const englishNotif = localeNotification(notification, 'en');
    const translatedNotif = localeNotification(notification, user.locale);
    return { englishNotif, translatedNotif };
  }

  await sendRevenuePushNotifications({
    day: 3,
    timezone: 21,
    getNotification,
    analyticsLabel: 'infinity-day-3-dm-promotion',
  });

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function day4Push(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  function getNotification(user) {
    const notification = {
      title: 'Take the chance on someone',
      body: "Don't be afraid to send that super love.",
      data: { premiumPopup: 'soulmate' },
    };
    const englishNotif = localeNotification(notification, 'en');
    const translatedNotif = localeNotification(notification, user.locale);
    return { englishNotif, translatedNotif };
  }

  await sendRevenuePushNotifications({
    day: 4,
    timezone: 21,
    getNotification,
    analyticsLabel: 'infinity-day-4-super-love-promotion',
  });

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function day5Push(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  function getNotification(user) {
    const notification = {
      title: 'Meet souls all around the world',
      body: 'Teleport using Boo Infinity, or set your recommendations to global mode and explore the alternate dimensions!',
      data: { premiumPopup: 'teleport' },
    };
    const englishNotif = localeNotification(notification, 'en');
    const translatedNotif = localeNotification(notification, user.locale);
    return { englishNotif, translatedNotif };
  }

  await sendRevenuePushNotifications({
    day: 5,
    timezone: 21,
    getNotification,
    analyticsLabel: 'infinity-day-5-teleport-promotion',
  });

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function inactiveReminder(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  {
    const query = {
      fcmToken: { $ne: null },
      'metrics.lastSeen': {
        $lt: DateTime.utc().minus({ days: 4 }).toJSDate(),
        $gt: DateTime.utc().minus({ days: 5 }).toJSDate(),
      },
      'pushNotificationSettings.dailyPush': { $ne: false },
      timezone: { $in: getTimezones(20) },
    };

    const projection = {
      fcmToken: 1,
      locale: 1,
    };
    const cursor = User.find(query, projection)
                       .read(readPreference, replicaTags)

    function getNotification(user) {
      const notification = {
        title: 'You\'ve been away for a while',
        body: 'Log back in everyday to continue to be shown to more souls.',
      };
      const englishNotif = localeNotification(notification, 'en');
      const translatedNotif = localeNotification(notification, user.locale);
      return { englishNotif, translatedNotif };
    }

    await sendTranslatedNotificationsUsingCursor(cursor, getNotification, 'inactive-reminder');
  }

  {
    const query = {
      fcmToken: { $ne: null },
      'metrics.lastSeen': {
        $lt: DateTime.utc().minus({ days: 2 }).toJSDate(),
        $gt: DateTime.utc().minus({ days: 3 }).toJSDate(),
      },
      'pushNotificationSettings.dailyPush': { $ne: false },
      timezone: { $in: getTimezones(20) },
      firstName: { $ne: '' },
    };

    const projection = {
      firstName: 1,
      fcmToken: 1,
      locale: 1,
    };
    const cursor = User.find(query, projection)
                       .read(readPreference, replicaTags)

    function getNotification(user) {
      const notification = {
        title: 'Keep it up {{name}}!',
        body: 'New souls join every day.',
      };
      const englishNotif = localeNotification(notification, 'en', { name: user.firstName });
      const translatedNotif = localeNotification(notification, user.locale, { name: user.firstName });
      return { englishNotif, translatedNotif };
    }

    await sendTranslatedNotificationsUsingCursor(cursor, getNotification, 'inactive-reminder-keep-it-up');
  }

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function sendEmailNotifications(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  let users;

  // inactive notification email
  for (const notificationDay of INACTIVE_REMINDER_DAYS) {
    const query = {
      $and: [
        {
          'metrics.lastSeen': {
            $lt: DateTime.utc().minus({ days: notificationDay }).toJSDate(),
            $gt: DateTime.utc().minus({ days: notificationDay + 1 }).toJSDate(),
          },
          timezone: { $in: getTimezones(13) },
          shadowBanned: { $ne: true },
        },
        {
          $or: [
            {
              ...emailBaseQuery,
            },
            {
              phoneNumber: { $regex: /^\+1.*/ },
              'pushNotificationSettings.sms': { $ne: false },
            },
          ],
        },
      ],
    };
    const projection = ['_id', 'locale', 'firstName', 'pictures', 'phoneNumber', 'shadowBanned', 'appVersion', 'translator', 'premiumExpiration', 'preferences', 'gender', 'scores', 'birthday', 'location', 'personality', 'moreAboutUser', 'genderPreferenceHash', 'recommendationsExhaustedAt', 'localLoops', 'countryLoops', 'countryGroupLoops','metrics.lastSeen'].concat(emailBaseProjection);
    users = await User.find(query, projection)
                      .read(readPreference, replicaTags)
    // console.log(users, 'in active users')
    // console.log(`Users eligible for ${notificationDay}-day email sms notifications: ${users.length}`);

    const notifyCounter = {
      notEligibleUsersCount: 0,
      likesEmail: 0,
      likesSms: 0,
      matchEmail: 0,
      matchSms: 0,
      messageEmail: 0,
      messageSms: 0,
      newRecEmail: 0,
      totalNotNotifiedEmail: 0,
      totalNotNotifiedSms: 0,
    };

    for (const user of users) {
      const eligibleForEmail = (user.email && !(user.pushNotificationSettings && user.pushNotificationSettings.email === false));

      const eligibleForSms = (!eligibleForEmail
        && !(user.pushNotificationSettings && user.pushNotificationSettings.sms === false)
        && phone(user.phoneNumber, { country: 'USA' }).isValid);

      if (!(eligibleForEmail || eligibleForSms)) {
        console.log(`Inactive User: ${user._id} not eligible for notifications`);
        notifyCounter.notEligibleUsersCount++;
        continue;
      }

      const approvedChats = await chatLib.getApprovedChats(user);

      const newMatch = approvedChats.find((x) => x.lastMessage === undefined && x.lastMessageTime > user.metrics.lastSeen);
      if (newMatch) {
        if (eligibleForSms) {
          const sms = smsNotifications.match(user);
          await sendSms(user.phoneNumber, sms);

          console.log(`New match sms: ${user._id} ${user.phoneNumber} ${sms}`);
          notifyCounter.matchSms++;
        } else {
          await emailLib.sendNewMatchEmail(user);
          notifyCounter.matchEmail++;
        }

        continue;
      }

      const unreadChat = approvedChats.find((x) => x.numUnreadMessages > 0 && x.lastMessageTime > user.metrics.lastSeen);

      if (unreadChat) {
        if (eligibleForSms) {
          const sms = smsNotifications.message(user);
          await sendSms(user.phoneNumber, sms);

          console.log(`New msg sms: ${user._id} ${user.phoneNumber} ${sms}`);
          notifyCounter.messageSms++;
        } else {
          await emailLib.sendNewMessageEmail(user, unreadChat.user.firstName);
          notifyCounter.messageEmail++;
        }
        continue;
      }

      const pendingChats = await chatLib.getPendingChats(user);
      const pendingChat = pendingChats.find((x) => x.numUnreadMessages > 0 && x.lastMessageTime > user.metrics.lastSeen);

      if (pendingChat) {
        if (eligibleForSms) {
          const sms = smsNotifications.like(user);
          await sendSms(user.phoneNumber, sms);

          console.log(`New like sms: ${user._id} ${user.phoneNumber} ${sms}`);
          notifyCounter.likesSms++;
        } else {
          await emailLib.sendNewLikeEmail(user);
          notifyCounter.likesEmail++;
        }
        continue;
      }

      if (eligibleForSms) {
        console.log(`No notification sms sent: ${user._id} ${user.phoneNumber}`);
        notifyCounter.totalNotNotifiedSms++;
        continue;
      }

      const profiles = await profilesLib3.getProfiles(user, 1);
      if (profiles.length > 0) {
        await emailLib.sendNewRecommendationsEmail(user);
        notifyCounter.newRecEmail++;
        continue;
      }

      console.log(`No notification email sent: ${user._id} ${user.email}`);
      notifyCounter.totalNotNotifiedEmail++;
    }

    console.log(`Notification Stats: ${JSON.stringify(notifyCounter)}`);
    console.log(`Total Email Sent: ${notifyCounter.likesEmail + notifyCounter.matchEmail + notifyCounter.messageEmail + notifyCounter.newRecEmail}`);
    console.log(`Total Sms Sent: ${notifyCounter.likesSms + notifyCounter.matchSms + notifyCounter.messageSms}`);
  }

  // 7-day inactive re-engagement email
  users = await User.find({
    'metrics.lastSeen': {
      $lt: DateTime.utc().minus({ days: 7 }).toJSDate(),
      $gt: DateTime.utc().minus({ days: 8 }).toJSDate(),
    },
    timezone: { $in: getTimezones(13) },
    shadowBanned: { $ne: true },
    ...emailBaseQuery,
  })
  .read(readPreference, replicaTags)

  console.log(`Users eligible for 7-day inactive email: ${users.length}`);

  for (const user of users) {
    await emailLib.sendInactiveEmail(user);
  }

  // 25-day deleted account email
  users = await User.find({
    deletionRequestDate: {
      $lt: DateTime.utc().minus({ days: 25 }).toJSDate(),
      $gt: DateTime.utc().minus({ days: 26 }).toJSDate(),
    },
    timezone: { $in: getTimezones(13) },
    shadowBanned: { $ne: true },
    ...emailBaseQuery,
  })
  .read(readPreference, replicaTags)

  console.log(`Users eligible for 25-day deleted account email: ${users.length}`);

  for (const user of users) {
    await emailLib.sendDeleteAccountEmail(user);
  }

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function dailyDigest(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  const query = {
    'metrics.lastSeen': {
      $lt: DateTime.utc().minus({ days: 1 }).toJSDate(),
      $gt: DateTime.utc().minus({ days: 7 }).toJSDate(),
    },
    timezone: { $in: getTimezones(21) },
    shadowBanned: { $ne: true },
    ...emailBaseQuery,
  };

  const projection = ['firstName'].concat(emailBaseProjection);

  await sendDailyDigestToCursor(query, projection);

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function cityNewUserPush(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  const USER_BATCH_SIZE = 1000;

  const userThreshold = cityLib.getCityUserThreshold();

  const cityStats = await LocationStat.find({ timezone: { $in: getTimezones(13) } }, { location: 1, userCount: 1, lastCount: 1 });

  for (const cityStat of cityStats) {
    const newUsersCount = cityStat.userCount - cityStat.lastCount;

    if (newUsersCount >= userThreshold) {
      let hasMoreUsers = true;
      // Process users in batches of USER_BATCH_SIZE to avoid 16MB limit and the metrics.lastNewJoinNotifiedAt filter will naturally prevent repeated processing
      while (hasMoreUsers) {
        let cityUsers = await User.find(
          {
            location: {
              $nearSphere: {
                $geometry: cityStat.location,
                $maxDistance: cityLib.getCityUserDistanceThreshold(),
              },
            },
            fcmToken: { $ne: null },
            'metrics.lastNewJoinNotifiedAt': { $not: { $gt: DateTime.utc().minus({ hours: 24 * 3 }).toJSDate() } },
            'pushNotificationSettings.newSoulsNearby': { $ne: false },
            firstName: { $ne: '', $exists: true }, // adding this filter to avoid processing them, to avoid infinite loop
          },
          {
            firstName: 1,
            fcmToken: 1,
            locale: 1,
            'events.newUsersJoinedCity': 1,
          },
        )
        .limit(USER_BATCH_SIZE)
        .read(readPreference, replicaTags)

        // If we got fewer than USER_BATCH_SIZE users, this is the last batch
        if (cityUsers.length < USER_BATCH_SIZE) hasMoreUsers = false;
        // If there are no users, break out of the loop
        if (cityUsers.length === 0) break; 

        await User.updateMany(
          {
            _id: { $in: cityUsers.map(userData => userData._id) } 
          },
          {
            $inc: { 'events.newUsersJoinedCity': 1 },
            $set: { 'metrics.lastNewJoinNotifiedAt':new Date() }
          })

        // send batch notifications
        let userIdTokenMap = new Map();
        let messages = [];
        for (const cityUser of cityUsers) {
          let analyticsLabel = 'new-souls-nearby';
          let notificationTitle = 'Boo'
          let notificationBody = `{{name}}, there has been an increase in souls nearby! See who.`
          const message = await generateMessageForNotification(
            cityUser,
            null,
            translate(notificationTitle, cityUser.locale, { name: cityUser.firstName }),
            translate(notificationBody, cityUser.locale, { name: cityUser.firstName }),
            null,
            null,
            'general',
            analyticsLabel,
          )
          if (message && message.token) {
            messages.push(message);
            userIdTokenMap.set(message.token, cityUser._id);
          }
        }
        await sendBatchNotifications(messages, userIdTokenMap);
      }
      cityStat.lastSentAt = Date.now();
      cityStat.lastCount = cityStat.userCount;
      await cityStat.save();
    }
  }

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function checkForBirthdayReward(user) {
  if (!user.birthday) {
    return;
  }

  const localTime = DateTime.local().setZone(user.timezone);
  if (localTime.month != (user.birthday.getMonth() + 1) || localTime.day != user.birthday.getDate()) {
    // not birthday
    return;
  }
  if (localTime.year == user.metrics.birthdayRewardReceivedYear) {
    // birthday reward already received this year
    return;
  }

  const coins = await coinsLib.updateCoins(
    {
      user: user._id,
    },
    {
      $inc: { coins: coinsConstants.birthdayReward },
    },
    'Birthday Reward',
  );
  if (!coins) {
    return;
  }

  /*
  const coinReward = [{
    caption: translate('Happy birthday, %s!', user.locale, user.firstName),
    rewardAmount: coinsConstants.birthdayReward,
    newTotal: coins,
  }];
  sendCoinRewards(user._id, coinReward);
  */

  user.metrics.birthdayRewardReceivedYear = localTime.year;
  await user.save();

  await sendNotification(
    user,
    null,
    translate('Happy birthday, %s!', user.locale, user.firstName),
    translate("We hope you have an amazing day today. Here's 50 coins on us.", user.locale),
    null,
    null,
    'general',
    'happy-birthday',
  );
}

async function sendBoostSuccessNotification(user, extraViews) {
  console.log(`[notifyBoostEffectiveness] Sending boost success notification to ${user._id} for ${extraViews} extra views`);

  const data = {
    openPage: "boostSuccessPopup",
    boostEffectiveness: `${extraViews}`,
  };

  await sendSocketEvent(user._id, 'triggerBoostSuccessPopup', data);

  let notificationBody = translate(`You were seen by 1 more soul in the last 60 minutes!`, user.locale);
  if (extraViews > 1) {
    notificationBody = translate(`You were seen by {{views}} more souls in the last 60 minutes!`, user.locale, { views: extraViews });
  }

  await sendNotification(
    user,
    null,
    translate('Your Boost was a success!', user.locale),
    notificationBody,
    data,
    null,
    'general',
    'boost-success',
    false,
    null,
    'numSuccessfulBoosts',
  );
}

async function birthdayPush(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  const timezones = getTimezones(12);
  const localTime = DateTime.local().setZone(timezones[0]);

  const cursor = User.find(
    {
      $and: [
        { fcmToken: { $ne: null } },
        { timezone: { $in: timezones } },
        { $expr: {$eq: [{$month: "$birthday"}, localTime.month]} },
        { $expr: {$eq: [{$dayOfMonth: "$birthday"}, localTime.day]} }
      ]
    },
    {
      firstName: 1,
      fcmToken: 1,
      locale: 1,
      birthday: 1,
      timezone: 1,
      'metrics.birthdayRewardReceivedYear': 1,
    },
  )
  .read(readPreference, replicaTags)

  let promisesArr = [];

  for await (const user of cursor) {
    promisesArr.push(checkForBirthdayReward(user));
    if (promisesArr.length > 25) {
      await Promise.all(promisesArr);
      promisesArr = [];
    }
  }
  await Promise.all(promisesArr);

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function endBoostLiveActivity(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  const users = await User.find({
    fcmToken: { $ne: null },
    boostActivityToken: { $exists: true },
    boostExpiration: { $lte: new Date() },
  }, 'fcmToken boostActivityToken');

  for (const user of users) {
    await endLiveActivityHTTPv1({
      fcmToken: user.fcmToken,
      liveActivityPushToken: user.boostActivityToken,
    });
    user.boostActivityToken = undefined;
    await user.save();
  }

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function notifyBoostEffectiveness(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  const now = new Date();
  const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);

  const users = await User.find({
    boostExpiration: {
      $gte: oneMinuteAgo,
      $lte: now,
    },
    fcmToken: { $type: 'string' },
  }, 'config boostExpiration metrics appVersion fcmToken locale');

  console.log(`[notifyBoostEffectiveness] Found ${users.length} users to process`);

  let promisesArr = [];
  for (const user of users) {
    const extraViews = await chatLib.calculateBoostEffectiveness(user);

    console.log(`[notifyBoostEffectiveness][${user._id}] config893=${user.isConfigTrue?.('app_893')}, boostEffectiveness=${extraViews}`);

    const shouldNotify = user.isConfigTrue?.('app_893') && extraViews >= 1;
    if (shouldNotify) {
      promisesArr.push(sendBoostSuccessNotification(user, extraViews));
      if (promisesArr.length > 25) {
        await Promise.all(promisesArr);
        promisesArr = [];
      }
    }

    await User.updateOne(
      { _id: user._id },
      {
        $unset: {
          boostExpiration: "",
        },
        ...(shouldNotify && { $set: { boostEffectiveness: extraViews } }),
      },
    );
  }
  if (promisesArr.length > 0) {
    await Promise.all(promisesArr);
  }

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

async function newArrivalsNotification(req, res, next) { // app_910
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  if(newArrivalsNotificationWorkerRunning){
    console.log('newArrivalsNotification job already running, skipping this job');
    if(process.env.TESTING) return res.json({});
    return
  }

  newArrivalsNotificationWorkerRunning = true; // to enable only 1 worker
  const FETCH_BATCH_SIZE = 500; // Fetch 500 users at a time

  try {
    while (true) {
      const now = new Date();
      let users = await User.find(
        {
          newArrivalsNotificationScheduledAt: { $lte: now },
          fcmToken: { $ne: null },
          'pushNotificationSettings.promotions': { $ne: false },
        }
      )
      .select({ _id: 1, fcmToken: 1, locale: 1, appVersion: 1 })
      .sort({ newArrivalsNotificationScheduledAt: 1 })
      .limit(FETCH_BATCH_SIZE)

      if(users.length === 0) break;

      let userIdTokenMap = new Map();
      let messages = [];
      for (const user of users) {
        const notification = {
          title: translate(`New souls just joined in your area!`, user.locale),
          body: translate(`Don't wait, see who now.`, user.locale),
          data: { premiumPopup: 'unlimitedLikes' },
          sound: 'general',
        };
        const message = await generateMessageForNotification(
          user,
          'promotions',
          notification.title,
          notification.body,
          notification.data,
          null,
          'general',
          'new-arrivals-re-engagement',
          false,
          null,
          'numNotificationsNewArrivals'
        )
        if (message && message.token) {
          messages.push(message);
          userIdTokenMap.set(message.token, user._id);
        }
      }

      try {
        const { successUserIds, failureUserIds } = await sendBatchNotifications(messages, userIdTokenMap, null, 'numNotificationsNewArrivals');
        if(successUserIds?.length > 0) {
          await User.updateMany({ _id: { $in: successUserIds }}, { $unset: {newArrivalsNotificationScheduledAt: 1 }});
          console.log(`newArrivalsNotification: Successfully processed ${successUserIds.length} users`);
        }
        if(failureUserIds?.length > 0) {
          console.log(`newArrivalsNotification: Failed to send notifications to ${failureUserIds.length} users`);
        }
      } catch (error) {
        console.error('newArrivalsNotification: Error in batch processing:', error);
        // Continue with next batch even if current batch fails
      }
    }
  } catch (error) {
    console.error('newArrivalsNotification: Critical error in main loop:', error);
  } finally {
    // Always reset the flag, regardless of success or failure
    newArrivalsNotificationWorkerRunning = false;
  }

  if (process.env.TESTING) {
    res.json({});
  }
}

module.exports = {
  dailyPush,
  cityNewUserPush,
  secondSale,
  inactiveReminder,
  updateUserScores,
  sendEmailNotifications,
  dailyDigest,
  INACTIVE_REMINDER_DAYS,
  smsNotifications,
  day1UniversesPush,
  day1SpiritRealmPush,
  day1UnlimitedLovesPush,
  day2Push,
  day3Push,
  day4Push,
  day5Push,
  recurringMonthlySale,
  superLikeSale,
  coinsSale,
  birthdayPush,
  notifyFlashSaleExpiration,
  boostPromoD1,
  boostSaleD3,
  endBoostLiveActivity,
  newArrivalsNotification,
  notifyBoostEffectiveness,
};
